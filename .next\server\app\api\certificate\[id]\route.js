/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/certificate/[id]/route";
exports.ids = ["app/api/certificate/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_certificate_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/certificate/[id]/route.ts */ \"(rsc)/./src/app/api/certificate/[id]/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_certificate_id_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_certificate_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/certificate/[id]/route\",\n        pathname: \"/api/certificate/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/certificate/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\certificate\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_certificate_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjZXJ0aWZpY2F0ZSUyRiU1QmlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZjZXJ0aWZpY2F0ZSUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY2VydGlmaWNhdGUlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNXaW5kb3dzJTIwMTElNUNEZXNrdG9wJTVDY29kZXMlNUNJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDV2luZG93cyUyMDExJTVDRGVza3RvcCU1Q2NvZGVzJTVDSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW0maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQzZEO0FBQzFJO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERlc2t0b3BcXFxcY29kZXNcXFxcSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2VydGlmaWNhdGVcXFxcW2lkXVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY2VydGlmaWNhdGUvW2lkXS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NlcnRpZmljYXRlL1tpZF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NlcnRpZmljYXRlL1tpZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERlc2t0b3BcXFxcY29kZXNcXFxcSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2VydGlmaWNhdGVcXFxcW2lkXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/certificate/[id]/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/certificate/[id]/route.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _lib_certificate_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/certificate-generator */ \"(rsc)/./src/lib/certificate-generator.ts\");\n/* harmony import */ var _lib_utils_certificate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/certificate */ \"(rsc)/./src/lib/utils/certificate.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nasync function GET(request, { params }) {\n    try {\n        // PUBLIC ACCESS - No authentication required for certificate download\n        const { id: resultId } = await params;\n        // Get test result with candidate info\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            testResult: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults,\n            candidate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId)).limit(1);\n        if (!result.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Test result not found'\n            }, {\n                status: 404\n            });\n        }\n        const { testResult, candidate } = result[0];\n        // Check if result is completed or verified (public access)\n        if (testResult.status !== 'completed' && testResult.status !== 'verified') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Certificate is only available for completed results'\n            }, {\n                status: 403\n            });\n        }\n        // Check if certificate has been generated\n        if (!testResult.certificateGenerated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Certificate has not been generated yet'\n            }, {\n                status: 404\n            });\n        }\n        // Check if all required scores are available\n        if (!testResult.overallBandScore) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Overall band score is required for certificate generation'\n            }, {\n                status: 400\n            });\n        }\n        // Generate the certificate PDF\n        const pdfBase64 = await (0,_lib_certificate_generator__WEBPACK_IMPORTED_MODULE_4__.generateCertificate)(testResult, candidate);\n        // Convert base64 to buffer\n        const pdfBuffer = Buffer.from(pdfBase64.split(',')[1], 'base64');\n        // Generate certificate serial if not exists\n        let certificateSerial = testResult.certificateSerial;\n        if (!certificateSerial) {\n            certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_5__.generateCertificateSerial)();\n        }\n        // Update the test result to mark certificate as generated\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set({\n            certificateGenerated: true,\n            certificateSerial: certificateSerial,\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId));\n        // Return the PDF as a downloadable file\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"IELTS_Certificate_${candidate.fullName.replace(/\\s+/g, '_')}_${resultId}.pdf\"`,\n                'Content-Length': pdfBuffer.length.toString()\n            }\n        });\n    } catch (error) {\n        console.error('Error generating certificate:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate certificate'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { id: resultId } = await params;\n        // Get test result with candidate info\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            testResult: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults,\n            candidate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId)).limit(1);\n        if (!result.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Test result not found'\n            }, {\n                status: 404\n            });\n        }\n        const { testResult, candidate } = result[0];\n        // Generate certificate serial if not exists\n        let certificateSerial = testResult.certificateSerial;\n        if (!certificateSerial) {\n            certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_5__.generateCertificateSerial)();\n        }\n        // Generate the certificate PDF\n        await (0,_lib_certificate_generator__WEBPACK_IMPORTED_MODULE_4__.generateCertificate)(testResult, candidate);\n        // Update the test result to mark certificate as generated\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set({\n            certificateGenerated: true,\n            certificateSerial: certificateSerial,\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Certificate generated successfully',\n            certificateId: resultId,\n            downloadUrl: `/api/certificate/${resultId}`\n        });\n    } catch (error) {\n        console.error('Error generating certificate:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate certificate'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/certificate/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/certificate-generator.ts":
/*!******************************************!*\
  !*** ./src/lib/certificate-generator.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCertificate: () => (/* binding */ generateCertificate)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(rsc)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nasync function generateCertificate(testResult, candidate) {\n    const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n    });\n    // Set up the certificate design\n    const pageWidth = pdf.internal.pageSize.getWidth();\n    const pageHeight = pdf.internal.pageSize.getHeight();\n    // ===== FRONT PAGE =====\n    createFrontPage(pdf, testResult, candidate, pageWidth, pageHeight);\n    // ===== BACK PAGE =====\n    pdf.addPage();\n    createBackPage(pdf, testResult, candidate, pageWidth, pageHeight);\n    // Convert to base64 string\n    const pdfBase64 = pdf.output('datauristring');\n    return pdfBase64;\n}\nfunction createFrontPage(pdf, testResult, candidate, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section with IELTS branding\n    pdf.setFillColor(0, 51, 102); // Dark blue background\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    // Main title - UPDATED (no logo)\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(20);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 30, {\n        align: 'center'\n    });\n    // Remove the old subtitle and validity text - they are no longer included\n    // Candidate details section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('CANDIDATE DETAILS', 15, 65);\n    // Create a box for candidate details\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 60);\n    // Candidate photo (right side) - ENHANCED LARGER PHOTO\n    const photoX = pageWidth - 55;\n    const photoY = 75;\n    const photoWidth = 35;\n    const photoHeight = 40;\n    // Photo border\n    pdf.setLineWidth(1);\n    pdf.rect(photoX, photoY, photoWidth, photoHeight);\n    // Add candidate photo if available\n    if (candidate.photoData) {\n        try {\n            // Prepare photo data\n            let photoData = candidate.photoData;\n            if (!photoData.startsWith('data:')) {\n                photoData = `data:image/jpeg;base64,${photoData}`;\n            }\n            // Add the photo with proper sizing\n            pdf.addImage(photoData, 'JPEG', photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2);\n        } catch (error) {\n            console.warn('Could not add photo to certificate:', error);\n            // Fallback placeholder text\n            pdf.setFontSize(8);\n            pdf.setTextColor(128, 128, 128);\n            pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n                align: 'center'\n            });\n            pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n                align: 'center'\n            });\n        }\n    } else {\n        // Placeholder text if no photo\n        pdf.setFontSize(8);\n        pdf.setTextColor(128, 128, 128);\n        pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n            align: 'center'\n        });\n        pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n            align: 'center'\n        });\n    }\n    // Candidate information (left side) - IMPROVED LAYOUT\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    const leftCol = 18;\n    const rightCol = 85; // Reduced to prevent overflow\n    pdf.text('Family Name:', leftCol, 85);\n    const familyName = candidate.fullName.split(' ').slice(-1)[0].toUpperCase();\n    pdf.text(familyName.length > 20 ? familyName.substring(0, 20) + '...' : familyName, rightCol, 85);\n    pdf.text('Given Name(s):', leftCol, 92);\n    const givenNames = candidate.fullName.split(' ').slice(0, -1).join(' ').toUpperCase();\n    pdf.text(givenNames.length > 20 ? givenNames.substring(0, 20) + '...' : givenNames, rightCol, 92);\n    pdf.text('Candidate Number:', leftCol, 99);\n    pdf.text(candidate.candidateNumber || 'N/A', rightCol, 99);\n    pdf.text('Date of Birth:', leftCol, 106);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(candidate.dateOfBirth)), rightCol, 106);\n    pdf.text('Identification Type:', leftCol, 113);\n    pdf.text('Passport', rightCol, 113);\n    pdf.text('Identification Number:', leftCol, 120);\n    const passportNum = candidate.passportNumber;\n    pdf.text(passportNum.length > 15 ? passportNum.substring(0, 15) + '...' : passportNum, rightCol, 120);\n    pdf.text('Country/Region of Origin:', leftCol, 127);\n    const nationality = candidate.nationality;\n    pdf.text(nationality.length > 15 ? nationality.substring(0, 15) + '...' : nationality, rightCol, 127);\n    // Test details section\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST DETAILS', 15, 145);\n    // Test details box\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 150, pageWidth - 30, 25);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Test Date:', 18, 160);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(candidate.testDate)), 85, 160);\n    pdf.text('Test Centre Number:', 18, 167);\n    pdf.text('SAM001', 85, 167); // Samarkand test center code\n    pdf.text('Test Centre Name:', 18, 174);\n    const testCenter = candidate.testCenter;\n    pdf.text(testCenter.length > 25 ? testCenter.substring(0, 25) + '...' : testCenter, 85, 174);\n    // Test Results section\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST RESULTS', 15, 190);\n    // Overall Band Score section (moved to top)\n    const overallY = 195;\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(15, overallY, pageWidth - 30, 25, 'F');\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(16);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('OVERALL BAND SCORE', 20, overallY + 10);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.overallBandScore) || 0), 90, overallY + 10);\n    pdf.text(getCEFRLevel(Number(testResult.overallBandScore) || 0), 140, overallY + 10);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.overallBandScore) || 0), 170, overallY + 10);\n    pdf.setFontSize(10);\n    pdf.text('This overall band score is the average of the four skills scores, rounded to the nearest half band.', 20, overallY + 20);\n    // Results table (moved below overall score)\n    const tableStartY = 230;\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, tableStartY, pageWidth - 30, 65);\n    // Table headers\n    pdf.setFillColor(240, 240, 240);\n    pdf.rect(15, tableStartY, pageWidth - 30, 15, 'F');\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('Skill', 20, tableStartY + 10);\n    pdf.text('Band Score', 70, tableStartY + 10);\n    pdf.text('CEFR Level', 110, tableStartY + 10);\n    pdf.text('Description', 140, tableStartY + 10);\n    // Horizontal lines for table structure - IMPROVED SPACING\n    pdf.line(15, tableStartY + 15, pageWidth - 15, tableStartY + 15);\n    pdf.line(65, tableStartY, 65, tableStartY + 65); // Skill column (reduced width)\n    pdf.line(105, tableStartY, 105, tableStartY + 65); // Band Score column (reduced width)\n    pdf.line(135, tableStartY, 135, tableStartY + 65); // CEFR column (reduced width)\n    // Test results data\n    const skills = [\n        {\n            name: 'Listening',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.listeningBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.listeningBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.listeningBandScore) || 0)\n        },\n        {\n            name: 'Reading',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.readingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.readingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.readingBandScore) || 0)\n        },\n        {\n            name: 'Writing',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.writingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.writingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.writingBandScore) || 0)\n        },\n        {\n            name: 'Speaking',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.speakingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.speakingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.speakingBandScore) || 0)\n        }\n    ];\n    pdf.setFont('helvetica', 'normal');\n    skills.forEach((skill, index)=>{\n        const rowY = tableStartY + 25 + index * 12;\n        // Horizontal line between rows\n        if (index > 0) {\n            pdf.line(15, rowY - 6, pageWidth - 15, rowY - 6);\n        }\n        pdf.text(skill.name, 20, rowY);\n        pdf.text(skill.band, 75, rowY);\n        pdf.text(skill.cefr, 115, rowY);\n        // Improved description handling - shorter text to prevent overflow\n        const description = skill.description;\n        if (description.length > 15) {\n            pdf.text(description.substring(0, 15) + '...', 145, rowY);\n        } else {\n            pdf.text(description, 145, rowY);\n        }\n    });\n}\nfunction createBackPage(pdf, testResult, candidate, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(18);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 25, {\n        align: 'center'\n    });\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Additional Information', pageWidth / 2, 35, {\n        align: 'center'\n    });\n    // Authentication section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('AUTHENTICATION', 15, 65);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 40);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Centre Stamp:', 20, 85);\n    pdf.text('Authorised Signature:', 20, 100);\n    // Certificate serial and verification info\n    pdf.text(`Certificate Serial: ${testResult.certificateSerial || 'N/A'}`, 120, 85);\n    pdf.text(`Issue Date: ${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date())}`, 120, 100);\n    // Band Score Descriptions\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('BAND SCORE DESCRIPTIONS', 15, 130);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 135, pageWidth - 30, 120);\n    pdf.setFontSize(9);\n    pdf.setFont('helvetica', 'normal');\n    const descriptions = [\n        'Band 9: Expert User - Has fully operational command of the language',\n        'Band 8: Very Good User - Has fully operational command with occasional inaccuracies',\n        'Band 7: Good User - Has operational command with occasional inaccuracies',\n        'Band 6: Competent User - Has generally effective command despite inaccuracies',\n        'Band 5: Modest User - Has partial command with frequent problems',\n        'Band 4: Limited User - Basic competence limited to familiar situations',\n        'Band 3: Extremely Limited User - Conveys general meaning in familiar situations',\n        'Band 2: Intermittent User - No real communication except basic information',\n        'Band 1: Non User - No ability to use the language'\n    ];\n    descriptions.forEach((desc, index)=>{\n        pdf.text(desc, 20, 145 + index * 12);\n    });\n    // Important Notes\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IMPORTANT NOTES', 15, 270);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 275, pageWidth - 30, 40);\n    pdf.setFontSize(9);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('• This is a MOCK certificate for practice purposes only', 20, 285);\n    pdf.text('• Not valid for official immigration or academic applications', 20, 295);\n    pdf.text('• For official IELTS certificates, visit www.ielts.org', 20, 305);\n}\n// Helper function to get CEFR level from band score\nfunction getCEFRLevel(bandScore) {\n    if (bandScore >= 8.5) return 'C2';\n    if (bandScore >= 7.0) return 'C1';\n    if (bandScore >= 5.5) return 'B2';\n    if (bandScore >= 4.0) return 'B1';\n    if (bandScore >= 3.0) return 'A2';\n    return 'A1';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/certificate-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3NjaGVtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0c7QUFDaEQ7QUFFaEQsaUNBQWlDO0FBQzFCLE1BQU1RLFFBQVFSLDREQUFPQSxDQUFDLFNBQVM7SUFDcENTLElBQUlSLHlEQUFJQSxDQUFDLE1BQU1TLFVBQVUsR0FBR0MsVUFBVSxDQUFDLElBQU1KLDhEQUFRQTtJQUNyREssTUFBTVgseURBQUlBLENBQUM7SUFDWFksT0FBT1oseURBQUlBLENBQUMsU0FBU2EsT0FBTyxHQUFHQyxNQUFNO0lBQ3JDQyxlQUFlZCw4REFBU0EsQ0FBQyxpQkFBaUI7UUFBRWUsTUFBTTtJQUFPO0lBQ3pEQyxPQUFPakIseURBQUlBLENBQUM7SUFDWmtCLFVBQVVsQix5REFBSUEsQ0FBQztJQUNmbUIsTUFBTW5CLHlEQUFJQSxDQUFDLFFBQVE7UUFBRW9CLE1BQU07WUFBQztZQUFTO1NBQWU7SUFBQyxHQUFHUCxPQUFPLEdBQUdRLE9BQU8sQ0FBQztJQUMxRUMsV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCwyQkFBMkI7QUFDcEIsTUFBTVksV0FBVzFCLDREQUFPQSxDQUFDLFlBQVk7SUFDMUMyQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGQyxNQUFNN0IseURBQUlBLENBQUMsUUFBUWEsT0FBTztJQUMxQmlCLFVBQVU5Qix5REFBSUEsQ0FBQyxZQUFZYSxPQUFPO0lBQ2xDa0IsbUJBQW1CL0IseURBQUlBLENBQUMscUJBQXFCYSxPQUFPO0lBQ3BEbUIsZUFBZWhDLHlEQUFJQSxDQUFDO0lBQ3BCaUMsY0FBY2pDLHlEQUFJQSxDQUFDO0lBQ25Ca0MsWUFBWWhDLDREQUFPQSxDQUFDO0lBQ3BCaUMsWUFBWW5DLHlEQUFJQSxDQUFDO0lBQ2pCb0MsT0FBT3BDLHlEQUFJQSxDQUFDO0lBQ1pxQyxVQUFVckMseURBQUlBLENBQUM7SUFDZnNDLGVBQWV0Qyx5REFBSUEsQ0FBQztBQUN0QixHQUFHO0FBRUgsb0NBQW9DO0FBQzdCLE1BQU11QyxXQUFXeEMsNERBQU9BLENBQUMsWUFBWTtJQUMxQ3lDLGNBQWN4Qyx5REFBSUEsQ0FBQyxnQkFBZ0JTLFVBQVU7SUFDN0NpQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGYSxTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsc0JBQXNCO0FBQ2YsTUFBTTZCLHFCQUFxQjNDLDREQUFPQSxDQUFDLHNCQUFzQjtJQUM5RDRDLFlBQVkzQyx5REFBSUEsQ0FBQyxjQUFjYSxPQUFPO0lBQ3RDK0IsT0FBTzVDLHlEQUFJQSxDQUFDLFNBQVNhLE9BQU87SUFDNUI0QixTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsbUJBQW1CO0FBQ1osTUFBTWdDLGFBQWE5Qyw0REFBT0EsQ0FBQyxjQUFjO0lBQzlDUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckR3QyxpQkFBaUI5Qyx5REFBSUEsQ0FBQyxvQkFBb0JhLE9BQU8sR0FBR0MsTUFBTTtJQUMxRGlDLFVBQVUvQyx5REFBSUEsQ0FBQyxhQUFhYSxPQUFPO0lBQ25DRCxPQUFPWix5REFBSUEsQ0FBQyxTQUFTYSxPQUFPLEdBQUdDLE1BQU07SUFDckNrQyxhQUFhaEQseURBQUlBLENBQUMsZ0JBQWdCYSxPQUFPO0lBQ3pDb0MsYUFBYWhELDhEQUFTQSxDQUFDLGlCQUFpQjtRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztJQUNqRXFDLGFBQWFsRCx5REFBSUEsQ0FBQyxlQUFlYSxPQUFPO0lBQ3hDc0MsZ0JBQWdCbkQseURBQUlBLENBQUMsbUJBQW1CYSxPQUFPLEdBQUdDLE1BQU07SUFDeERzQyxVQUFVbkQsOERBQVNBLENBQUMsYUFBYTtRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztJQUMxRHdDLFlBQVlyRCx5REFBSUEsQ0FBQyxlQUFlYSxPQUFPO0lBQ3ZDeUMsVUFBVXRELHlEQUFJQSxDQUFDO0lBQ2Z1RCxXQUFXdkQseURBQUlBLENBQUM7SUFDaEJzQixXQUFXckIsOERBQVNBLENBQUMsY0FBY3NCLFVBQVUsR0FBR1YsT0FBTztJQUN2RFcsV0FBV3ZCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87QUFDekQsR0FBRztBQUVILHFCQUFxQjtBQUNkLE1BQU0yQyxjQUFjekQsNERBQU9BLENBQUMsZ0JBQWdCO0lBQ2pEUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckRtRCxhQUFhekQseURBQUlBLENBQUMsZ0JBQWdCYSxPQUFPLEdBQUdjLFVBQVUsQ0FBQyxJQUFNa0IsV0FBV3JDLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBRWxHLG1CQUFtQjtJQUNuQjhCLGdCQUFnQnZELDREQUFPQSxDQUFDLG1CQUFtQjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDcEVDLG9CQUFvQjFELDREQUFPQSxDQUFDLHdCQUF3QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFN0UsaUJBQWlCO0lBQ2pCRSxjQUFjM0QsNERBQU9BLENBQUMsaUJBQWlCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUNoRUcsa0JBQWtCNUQsNERBQU9BLENBQUMsc0JBQXNCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUV6RSxpQkFBaUI7SUFDakJJLG1CQUFtQjdELDREQUFPQSxDQUFDLHVCQUF1QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDM0VLLG1CQUFtQjlELDREQUFPQSxDQUFDLHVCQUF1QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDM0VNLGtCQUFrQi9ELDREQUFPQSxDQUFDLHNCQUFzQjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFekUsa0JBQWtCO0lBQ2xCTyxzQkFBc0JoRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGUSxzQkFBc0JqRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGUyxzQkFBc0JsRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGVSw0QkFBNEJuRSw0REFBT0EsQ0FBQyxnQ0FBZ0M7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQzdGVyxtQkFBbUJwRSw0REFBT0EsQ0FBQyx1QkFBdUI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBRTNFLGdCQUFnQjtJQUNoQlksa0JBQWtCckUsNERBQU9BLENBQUMsc0JBQXNCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUV6RSxzQkFBc0I7SUFDdEJhLFFBQVF6RSx5REFBSUEsQ0FBQyxVQUFVO1FBQUVvQixNQUFNO1lBQUM7WUFBVztZQUFhO1NBQVc7SUFBQyxHQUFHUCxPQUFPLEdBQUdRLE9BQU8sQ0FBQztJQUN6RnFELFdBQVcxRSx5REFBSUEsQ0FBQyxjQUFjMkIsVUFBVSxDQUFDLElBQU1wQixNQUFNQyxFQUFFO0lBQ3ZEbUUsWUFBWTNFLHlEQUFJQSxDQUFDLGVBQWUyQixVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUU7SUFDekRvRSxzQkFBc0J4RSw0REFBT0EsQ0FBQyx5QkFBeUJpQixPQUFPLENBQUM7SUFDL0R3RCxtQkFBbUI3RSx5REFBSUEsQ0FBQyxzQkFBc0JjLE1BQU07SUFDcERnRSxnQkFBZ0I5RSx5REFBSUEsQ0FBQztJQUNyQitFLHFCQUFxQjNFLDREQUFPQSxDQUFDLHlCQUF5QmlCLE9BQU8sQ0FBQztJQUM5RCtCLFVBQVVuRCw4REFBU0EsQ0FBQyxhQUFhO1FBQUVlLE1BQU07SUFBTztJQUVoRE0sV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCxvQkFBb0I7QUFDYixNQUFNbUUsYUFBYWpGLDREQUFPQSxDQUFDLGVBQWU7SUFDL0NTLElBQUlSLHlEQUFJQSxDQUFDLE1BQU1TLFVBQVUsR0FBR0MsVUFBVSxDQUFDLElBQU1KLDhEQUFRQTtJQUNyRDJFLGNBQWNqRix5REFBSUEsQ0FBQyxrQkFBa0JhLE9BQU8sR0FBR2MsVUFBVSxDQUFDLElBQU02QixZQUFZaEQsRUFBRSxFQUFFO1FBQUVvQixVQUFVO0lBQVU7SUFFdEcsMEJBQTBCO0lBQzFCc0QsbUJBQW1CbEYseURBQUlBLENBQUM7SUFDeEJtRixpQkFBaUJuRix5REFBSUEsQ0FBQztJQUN0Qm9GLGlCQUFpQnBGLHlEQUFJQSxDQUFDO0lBQ3RCcUYsa0JBQWtCckYseURBQUlBLENBQUM7SUFFdkIsdUNBQXVDO0lBQ3ZDc0YsaUJBQWlCdEYseURBQUlBLENBQUM7SUFDdEJ1RixzQkFBc0J2Rix5REFBSUEsQ0FBQztJQUUzQiwyQkFBMkI7SUFDM0J3RixXQUFXbkYseURBQUlBLENBQUMsYUFBYW9GLEtBQUs7SUFDbENDLFlBQVlyRix5REFBSUEsQ0FBQyxjQUFjb0YsS0FBSztJQUVwQyx5QkFBeUI7SUFDekJFLFdBQVd0Rix5REFBSUEsQ0FBQyxjQUFjb0YsS0FBSztJQU9uQ0csYUFBYTNGLDhEQUFTQSxDQUFDLGdCQUFnQnNCLFVBQVUsR0FBR1YsT0FBTztBQUM3RCxHQUFHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbVxcc3JjXFxsaWJcXGRiXFxzY2hlbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGdUYWJsZSwgdGV4dCwgdGltZXN0YW1wLCBpbnRlZ2VyLCBkZWNpbWFsLCBib29sZWFuLCBqc29uIH0gZnJvbSAnZHJpenpsZS1vcm0vcGctY29yZSc7XHJcbmltcG9ydCB7IGNyZWF0ZUlkIH0gZnJvbSAnQHBhcmFsbGVsZHJpdmUvY3VpZDInO1xyXG5cclxuLy8gVXNlcnMgdGFibGUgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBjb25zdCB1c2VycyA9IHBnVGFibGUoJ3VzZXJzJywge1xyXG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxyXG4gIG5hbWU6IHRleHQoJ25hbWUnKSxcclxuICBlbWFpbDogdGV4dCgnZW1haWwnKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgZW1haWxWZXJpZmllZDogdGltZXN0YW1wKCdlbWFpbFZlcmlmaWVkJywgeyBtb2RlOiAnZGF0ZScgfSksXHJcbiAgaW1hZ2U6IHRleHQoJ2ltYWdlJyksXHJcbiAgcGFzc3dvcmQ6IHRleHQoJ3Bhc3N3b3JkJyksIC8vIEFkZGVkIHBhc3N3b3JkIGZpZWxkIGZvciBhdXRoZW50aWNhdGlvblxyXG4gIHJvbGU6IHRleHQoJ3JvbGUnLCB7IGVudW06IFsnYWRtaW4nLCAndGVzdF9jaGVja2VyJ10gfSkubm90TnVsbCgpLmRlZmF1bHQoJ3Rlc3RfY2hlY2tlcicpLFxyXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcclxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gQWNjb3VudHMgdGFibGUgZm9yIE9BdXRoXHJcbmV4cG9ydCBjb25zdCBhY2NvdW50cyA9IHBnVGFibGUoJ2FjY291bnRzJywge1xyXG4gIHVzZXJJZDogdGV4dCgndXNlcklkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuICB0eXBlOiB0ZXh0KCd0eXBlJykubm90TnVsbCgpLFxyXG4gIHByb3ZpZGVyOiB0ZXh0KCdwcm92aWRlcicpLm5vdE51bGwoKSxcclxuICBwcm92aWRlckFjY291bnRJZDogdGV4dCgncHJvdmlkZXJBY2NvdW50SWQnKS5ub3ROdWxsKCksXHJcbiAgcmVmcmVzaF90b2tlbjogdGV4dCgncmVmcmVzaF90b2tlbicpLFxyXG4gIGFjY2Vzc190b2tlbjogdGV4dCgnYWNjZXNzX3Rva2VuJyksXHJcbiAgZXhwaXJlc19hdDogaW50ZWdlcignZXhwaXJlc19hdCcpLFxyXG4gIHRva2VuX3R5cGU6IHRleHQoJ3Rva2VuX3R5cGUnKSxcclxuICBzY29wZTogdGV4dCgnc2NvcGUnKSxcclxuICBpZF90b2tlbjogdGV4dCgnaWRfdG9rZW4nKSxcclxuICBzZXNzaW9uX3N0YXRlOiB0ZXh0KCdzZXNzaW9uX3N0YXRlJyksXHJcbn0pO1xyXG5cclxuLy8gU2Vzc2lvbnMgdGFibGUgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBjb25zdCBzZXNzaW9ucyA9IHBnVGFibGUoJ3Nlc3Npb25zJywge1xyXG4gIHNlc3Npb25Ub2tlbjogdGV4dCgnc2Vzc2lvblRva2VuJykucHJpbWFyeUtleSgpLFxyXG4gIHVzZXJJZDogdGV4dCgndXNlcklkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuICBleHBpcmVzOiB0aW1lc3RhbXAoJ2V4cGlyZXMnLCB7IG1vZGU6ICdkYXRlJyB9KS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gVmVyaWZpY2F0aW9uIHRva2Vuc1xyXG5leHBvcnQgY29uc3QgdmVyaWZpY2F0aW9uVG9rZW5zID0gcGdUYWJsZSgndmVyaWZpY2F0aW9uVG9rZW5zJywge1xyXG4gIGlkZW50aWZpZXI6IHRleHQoJ2lkZW50aWZpZXInKS5ub3ROdWxsKCksXHJcbiAgdG9rZW46IHRleHQoJ3Rva2VuJykubm90TnVsbCgpLFxyXG4gIGV4cGlyZXM6IHRpbWVzdGFtcCgnZXhwaXJlcycsIHsgbW9kZTogJ2RhdGUnIH0pLm5vdE51bGwoKSxcclxufSk7XHJcblxyXG4vLyBDYW5kaWRhdGVzIHRhYmxlXHJcbmV4cG9ydCBjb25zdCBjYW5kaWRhdGVzID0gcGdUYWJsZSgnY2FuZGlkYXRlcycsIHtcclxuICBpZDogdGV4dCgnaWQnKS5wcmltYXJ5S2V5KCkuJGRlZmF1bHRGbigoKSA9PiBjcmVhdGVJZCgpKSxcclxuICBjYW5kaWRhdGVOdW1iZXI6IHRleHQoJ2NhbmRpZGF0ZV9udW1iZXInKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgZnVsbE5hbWU6IHRleHQoJ2Z1bGxfbmFtZScpLm5vdE51bGwoKSxcclxuICBlbWFpbDogdGV4dCgnZW1haWwnKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgcGhvbmVOdW1iZXI6IHRleHQoJ3Bob25lX251bWJlcicpLm5vdE51bGwoKSxcclxuICBkYXRlT2ZCaXJ0aDogdGltZXN0YW1wKCdkYXRlX29mX2JpcnRoJywgeyBtb2RlOiAnZGF0ZScgfSkubm90TnVsbCgpLFxyXG4gIG5hdGlvbmFsaXR5OiB0ZXh0KCduYXRpb25hbGl0eScpLm5vdE51bGwoKSxcclxuICBwYXNzcG9ydE51bWJlcjogdGV4dCgncGFzc3BvcnRfbnVtYmVyJykubm90TnVsbCgpLnVuaXF1ZSgpLFxyXG4gIHRlc3REYXRlOiB0aW1lc3RhbXAoJ3Rlc3RfZGF0ZScsIHsgbW9kZTogJ2RhdGUnIH0pLm5vdE51bGwoKSxcclxuICB0ZXN0Q2VudGVyOiB0ZXh0KCd0ZXN0X2NlbnRlcicpLm5vdE51bGwoKSxcclxuICBwaG90b1VybDogdGV4dCgncGhvdG9fdXJsJyksXHJcbiAgcGhvdG9EYXRhOiB0ZXh0KCdwaG90b19kYXRhJyksIC8vIEJhc2U2NCBlbmNvZGVkIHBob3RvIGRhdGFcclxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcCgnY3JlYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoJ3VwZGF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxyXG59KTtcclxuXHJcbi8vIFRlc3QgcmVzdWx0cyB0YWJsZVxyXG5leHBvcnQgY29uc3QgdGVzdFJlc3VsdHMgPSBwZ1RhYmxlKCd0ZXN0X3Jlc3VsdHMnLCB7XHJcbiAgaWQ6IHRleHQoJ2lkJykucHJpbWFyeUtleSgpLiRkZWZhdWx0Rm4oKCkgPT4gY3JlYXRlSWQoKSksXHJcbiAgY2FuZGlkYXRlSWQ6IHRleHQoJ2NhbmRpZGF0ZV9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IGNhbmRpZGF0ZXMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuXHJcbiAgLy8gTGlzdGVuaW5nIHNjb3Jlc1xyXG4gIGxpc3RlbmluZ1Njb3JlOiBkZWNpbWFsKCdsaXN0ZW5pbmdfc2NvcmUnLCB7IHByZWNpc2lvbjogMywgc2NhbGU6IDEgfSksXHJcbiAgbGlzdGVuaW5nQmFuZFNjb3JlOiBkZWNpbWFsKCdsaXN0ZW5pbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcclxuXHJcbiAgLy8gUmVhZGluZyBzY29yZXNcclxuICByZWFkaW5nU2NvcmU6IGRlY2ltYWwoJ3JlYWRpbmdfc2NvcmUnLCB7IHByZWNpc2lvbjogMywgc2NhbGU6IDEgfSksXHJcbiAgcmVhZGluZ0JhbmRTY29yZTogZGVjaW1hbCgncmVhZGluZ19iYW5kX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG5cclxuICAvLyBXcml0aW5nIHNjb3Jlc1xyXG4gIHdyaXRpbmdUYXNrMVNjb3JlOiBkZWNpbWFsKCd3cml0aW5nX3Rhc2sxX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG4gIHdyaXRpbmdUYXNrMlNjb3JlOiBkZWNpbWFsKCd3cml0aW5nX3Rhc2syX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG4gIHdyaXRpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3dyaXRpbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcclxuXHJcbiAgLy8gU3BlYWtpbmcgc2NvcmVzXHJcbiAgc3BlYWtpbmdGbHVlbmN5U2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2ZsdWVuY3lfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdMZXhpY2FsU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2xleGljYWxfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdHcmFtbWFyU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2dyYW1tYXJfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdQcm9udW5jaWF0aW9uU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX3Byb251bmNpYXRpb25fc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2JhbmRfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcblxyXG4gIC8vIE92ZXJhbGwgc2NvcmVcclxuICBvdmVyYWxsQmFuZFNjb3JlOiBkZWNpbWFsKCdvdmVyYWxsX2JhbmRfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcblxyXG4gIC8vIFN0YXR1cyBhbmQgbWV0YWRhdGFcclxuICBzdGF0dXM6IHRleHQoJ3N0YXR1cycsIHsgZW51bTogWydwZW5kaW5nJywgJ2NvbXBsZXRlZCcsICd2ZXJpZmllZCddIH0pLm5vdE51bGwoKS5kZWZhdWx0KCdwZW5kaW5nJyksXHJcbiAgZW50ZXJlZEJ5OiB0ZXh0KCdlbnRlcmVkX2J5JykucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCksXHJcbiAgdmVyaWZpZWRCeTogdGV4dCgndmVyaWZpZWRfYnknKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcclxuICBjZXJ0aWZpY2F0ZUdlbmVyYXRlZDogYm9vbGVhbignY2VydGlmaWNhdGVfZ2VuZXJhdGVkJykuZGVmYXVsdChmYWxzZSksXHJcbiAgY2VydGlmaWNhdGVTZXJpYWw6IHRleHQoJ2NlcnRpZmljYXRlX3NlcmlhbCcpLnVuaXF1ZSgpLFxyXG4gIGNlcnRpZmljYXRlVXJsOiB0ZXh0KCdjZXJ0aWZpY2F0ZV91cmwnKSxcclxuICBhaUZlZWRiYWNrR2VuZXJhdGVkOiBib29sZWFuKCdhaV9mZWVkYmFja19nZW5lcmF0ZWQnKS5kZWZhdWx0KGZhbHNlKSxcclxuICB0ZXN0RGF0ZTogdGltZXN0YW1wKCd0ZXN0X2RhdGUnLCB7IG1vZGU6ICdkYXRlJyB9KSxcclxuXHJcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxyXG4gIHVwZGF0ZWRBdDogdGltZXN0YW1wKCd1cGRhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcclxufSk7XHJcblxyXG4vLyBBSSBmZWVkYmFjayB0YWJsZVxyXG5leHBvcnQgY29uc3QgYWlGZWVkYmFjayA9IHBnVGFibGUoJ2FpX2ZlZWRiYWNrJywge1xyXG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxyXG4gIHRlc3RSZXN1bHRJZDogdGV4dCgndGVzdF9yZXN1bHRfaWQnKS5ub3ROdWxsKCkucmVmZXJlbmNlcygoKSA9PiB0ZXN0UmVzdWx0cy5pZCwgeyBvbkRlbGV0ZTogJ2Nhc2NhZGUnIH0pLFxyXG5cclxuICAvLyBGZWVkYmFjayBmb3IgZWFjaCBza2lsbFxyXG4gIGxpc3RlbmluZ0ZlZWRiYWNrOiB0ZXh0KCdsaXN0ZW5pbmdfZmVlZGJhY2snKSxcclxuICByZWFkaW5nRmVlZGJhY2s6IHRleHQoJ3JlYWRpbmdfZmVlZGJhY2snKSxcclxuICB3cml0aW5nRmVlZGJhY2s6IHRleHQoJ3dyaXRpbmdfZmVlZGJhY2snKSxcclxuICBzcGVha2luZ0ZlZWRiYWNrOiB0ZXh0KCdzcGVha2luZ19mZWVkYmFjaycpLFxyXG5cclxuICAvLyBPdmVyYWxsIGZlZWRiYWNrIGFuZCByZWNvbW1lbmRhdGlvbnNcclxuICBvdmVyYWxsRmVlZGJhY2s6IHRleHQoJ292ZXJhbGxfZmVlZGJhY2snKSxcclxuICBzdHVkeVJlY29tbWVuZGF0aW9uczogdGV4dCgnc3R1ZHlfcmVjb21tZW5kYXRpb25zJyksXHJcblxyXG4gIC8vIFN0cmVuZ3RocyBhbmQgd2Vha25lc3Nlc1xyXG4gIHN0cmVuZ3RoczoganNvbignc3RyZW5ndGhzJykuJHR5cGU8c3RyaW5nW10+KCksXHJcbiAgd2Vha25lc3NlczoganNvbignd2Vha25lc3NlcycpLiR0eXBlPHN0cmluZ1tdPigpLFxyXG5cclxuICAvLyBTdHVkeSBwbGFuIHN1Z2dlc3Rpb25zXHJcbiAgc3R1ZHlQbGFuOiBqc29uKCdzdHVkeV9wbGFuJykuJHR5cGU8e1xyXG4gICAgdGltZWZyYW1lOiBzdHJpbmc7XHJcbiAgICBmb2N1c0FyZWFzOiBzdHJpbmdbXTtcclxuICAgIHJlc291cmNlczogc3RyaW5nW107XHJcbiAgICBwcmFjdGljZUFjdGl2aXRpZXM6IHN0cmluZ1tdO1xyXG4gIH0+KCksXHJcblxyXG4gIGdlbmVyYXRlZEF0OiB0aW1lc3RhbXAoJ2dlbmVyYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gRXhwb3J0IHR5cGVzXHJcbmV4cG9ydCB0eXBlIFVzZXIgPSB0eXBlb2YgdXNlcnMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdVc2VyID0gdHlwZW9mIHVzZXJzLiRpbmZlckluc2VydDtcclxuZXhwb3J0IHR5cGUgQ2FuZGlkYXRlID0gdHlwZW9mIGNhbmRpZGF0ZXMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdDYW5kaWRhdGUgPSB0eXBlb2YgY2FuZGlkYXRlcy4kaW5mZXJJbnNlcnQ7XHJcbmV4cG9ydCB0eXBlIFRlc3RSZXN1bHQgPSB0eXBlb2YgdGVzdFJlc3VsdHMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdUZXN0UmVzdWx0ID0gdHlwZW9mIHRlc3RSZXN1bHRzLiRpbmZlckluc2VydDtcclxuZXhwb3J0IHR5cGUgQUlGZWVkYmFjayA9IHR5cGVvZiBhaUZlZWRiYWNrLiRpbmZlclNlbGVjdDtcclxuZXhwb3J0IHR5cGUgTmV3QUlGZWVkYmFjayA9IHR5cGVvZiBhaUZlZWRiYWNrLiRpbmZlckluc2VydDtcclxuIl0sIm5hbWVzIjpbInBnVGFibGUiLCJ0ZXh0IiwidGltZXN0YW1wIiwiaW50ZWdlciIsImRlY2ltYWwiLCJib29sZWFuIiwianNvbiIsImNyZWF0ZUlkIiwidXNlcnMiLCJpZCIsInByaW1hcnlLZXkiLCIkZGVmYXVsdEZuIiwibmFtZSIsImVtYWlsIiwibm90TnVsbCIsInVuaXF1ZSIsImVtYWlsVmVyaWZpZWQiLCJtb2RlIiwiaW1hZ2UiLCJwYXNzd29yZCIsInJvbGUiLCJlbnVtIiwiZGVmYXVsdCIsImNyZWF0ZWRBdCIsImRlZmF1bHROb3ciLCJ1cGRhdGVkQXQiLCJhY2NvdW50cyIsInVzZXJJZCIsInJlZmVyZW5jZXMiLCJvbkRlbGV0ZSIsInR5cGUiLCJwcm92aWRlciIsInByb3ZpZGVyQWNjb3VudElkIiwicmVmcmVzaF90b2tlbiIsImFjY2Vzc190b2tlbiIsImV4cGlyZXNfYXQiLCJ0b2tlbl90eXBlIiwic2NvcGUiLCJpZF90b2tlbiIsInNlc3Npb25fc3RhdGUiLCJzZXNzaW9ucyIsInNlc3Npb25Ub2tlbiIsImV4cGlyZXMiLCJ2ZXJpZmljYXRpb25Ub2tlbnMiLCJpZGVudGlmaWVyIiwidG9rZW4iLCJjYW5kaWRhdGVzIiwiY2FuZGlkYXRlTnVtYmVyIiwiZnVsbE5hbWUiLCJwaG9uZU51bWJlciIsImRhdGVPZkJpcnRoIiwibmF0aW9uYWxpdHkiLCJwYXNzcG9ydE51bWJlciIsInRlc3REYXRlIiwidGVzdENlbnRlciIsInBob3RvVXJsIiwicGhvdG9EYXRhIiwidGVzdFJlc3VsdHMiLCJjYW5kaWRhdGVJZCIsImxpc3RlbmluZ1Njb3JlIiwicHJlY2lzaW9uIiwic2NhbGUiLCJsaXN0ZW5pbmdCYW5kU2NvcmUiLCJyZWFkaW5nU2NvcmUiLCJyZWFkaW5nQmFuZFNjb3JlIiwid3JpdGluZ1Rhc2sxU2NvcmUiLCJ3cml0aW5nVGFzazJTY29yZSIsIndyaXRpbmdCYW5kU2NvcmUiLCJzcGVha2luZ0ZsdWVuY3lTY29yZSIsInNwZWFraW5nTGV4aWNhbFNjb3JlIiwic3BlYWtpbmdHcmFtbWFyU2NvcmUiLCJzcGVha2luZ1Byb251bmNpYXRpb25TY29yZSIsInNwZWFraW5nQmFuZFNjb3JlIiwib3ZlcmFsbEJhbmRTY29yZSIsInN0YXR1cyIsImVudGVyZWRCeSIsInZlcmlmaWVkQnkiLCJjZXJ0aWZpY2F0ZUdlbmVyYXRlZCIsImNlcnRpZmljYXRlU2VyaWFsIiwiY2VydGlmaWNhdGVVcmwiLCJhaUZlZWRiYWNrR2VuZXJhdGVkIiwiYWlGZWVkYmFjayIsInRlc3RSZXN1bHRJZCIsImxpc3RlbmluZ0ZlZWRiYWNrIiwicmVhZGluZ0ZlZWRiYWNrIiwid3JpdGluZ0ZlZWRiYWNrIiwic3BlYWtpbmdGZWVkYmFjayIsIm92ZXJhbGxGZWVkYmFjayIsInN0dWR5UmVjb21tZW5kYXRpb25zIiwic3RyZW5ndGhzIiwiJHR5cGUiLCJ3ZWFrbmVzc2VzIiwic3R1ZHlQbGFuIiwiZ2VuZXJhdGVkQXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallBandScore: () => (/* binding */ calculateOverallBandScore),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBandScore: () => (/* binding */ formatBandScore),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getBandScoreDescription: () => (/* binding */ getBandScoreDescription),\n/* harmony export */   getListeningBandScore: () => (/* binding */ getListeningBandScore),\n/* harmony export */   getReadingBandScore: () => (/* binding */ getReadingBandScore)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// IELTS band score calculation utilities\nfunction calculateOverallBandScore(listening, reading, writing, speaking) {\n    const average = (listening + reading + writing + speaking) / 4;\n    // Round to nearest 0.5\n    return Math.round(average * 2) / 2;\n}\nfunction getListeningBandScore(rawScore) {\n    // IELTS Listening band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction getReadingBandScore(rawScore) {\n    // IELTS Reading band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatBandScore(score) {\n    return score.toFixed(1);\n}\nfunction getBandScoreDescription(score) {\n    if (score >= 9.0) return 'Expert User';\n    if (score >= 8.0) return 'Very Good User';\n    if (score >= 7.0) return 'Good User';\n    if (score >= 6.0) return 'Competent User';\n    if (score >= 5.0) return 'Modest User';\n    if (score >= 4.0) return 'Limited User';\n    if (score >= 3.0) return 'Extremely Limited User';\n    if (score >= 2.0) return 'Intermittent User';\n    if (score >= 1.0) return 'Non User';\n    return 'Did not attempt the test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/certificate.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/certificate.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractYearFromSerial: () => (/* binding */ extractYearFromSerial),\n/* harmony export */   formatCertificateSerial: () => (/* binding */ formatCertificateSerial),\n/* harmony export */   generateCertificateSerial: () => (/* binding */ generateCertificateSerial),\n/* harmony export */   validateCertificateSerial: () => (/* binding */ validateCertificateSerial)\n/* harmony export */ });\n/**\n * Certificate utility functions for IELTS certification system\n */ /**\n * Generate a unique certificate serial number\n * Format: IELTS-YYYY-NNNNNN (e.g., IELTS-2024-123456)\n */ function generateCertificateSerial() {\n    const year = new Date().getFullYear();\n    const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number\n    return `IELTS-${year}-${randomNumber}`;\n}\n/**\n * Validate certificate serial number format\n */ function validateCertificateSerial(serial) {\n    const pattern = /^IELTS-\\d{4}-\\d{6}$/;\n    return pattern.test(serial);\n}\n/**\n * Extract year from certificate serial\n */ function extractYearFromSerial(serial) {\n    const match = serial.match(/^IELTS-(\\d{4})-\\d{6}$/);\n    return match ? parseInt(match[1], 10) : null;\n}\n/**\n * Format certificate serial for display\n */ function formatCertificateSerial(serial) {\n    if (!validateCertificateSerial(serial)) {\n        return serial;\n    }\n    // Add spaces for better readability: IELTS-2024-123456 -> IELTS 2024 123456\n    return serial.replace(/-/g, ' ');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/certificate.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva","vendor-chunks/@babel","vendor-chunks/tailwind-merge","vendor-chunks/fflate","vendor-chunks/clsx","vendor-chunks/jspdf"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcertificate%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();