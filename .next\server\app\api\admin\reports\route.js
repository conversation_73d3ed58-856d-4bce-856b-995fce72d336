/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/reports/route";
exports.ids = ["app/api/admin/reports/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freports%2Froute&page=%2Fapi%2Fadmin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freports%2Froute&page=%2Fapi%2Fadmin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/reports/route.ts */ \"(rsc)/./src/app/api/admin/reports/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/reports/route\",\n        pathname: \"/api/admin/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_reports_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freports%2Froute&page=%2Fapi%2Fadmin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/reports/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/admin/reports/route.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/sql.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { from, to } = await request.json();\n        const fromDate = new Date(from);\n        const toDate = new Date(to);\n        // Overview statistics\n        const totalCandidatesResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, toDate)));\n        const totalResultsResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, toDate)));\n        const averageBandScoreResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            avg: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.avg)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore)\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, toDate)));\n        const certificatesGeneratedResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.certificateGenerated, true), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, toDate)));\n        // Band score distribution\n        const bandScoreDistribution = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            score: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, toDate))).groupBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore);\n        // Calculate percentages for band score distribution\n        const totalForDistribution = bandScoreDistribution.reduce((sum, item)=>sum + item.count, 0);\n        const processedBandDistribution = bandScoreDistribution.filter((item)=>item.score !== null).map((item)=>({\n                score: item.score.toString(),\n                count: item.count,\n                percentage: totalForDistribution > 0 ? item.count / totalForDistribution * 100 : 0\n            }));\n        // Test center statistics\n        const testCenterStats = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            center: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter,\n            candidates: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)(),\n            avgScore: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.avg)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore)\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, toDate))).groupBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter);\n        // Monthly statistics\n        const monthlyStats = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            month: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.sql)`TO_CHAR(${_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt}, 'YYYY-MM')`,\n            candidates: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id),\n            results: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id),\n            avgScore: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.avg)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore)\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, fromDate), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, toDate))).groupBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.sql)`TO_CHAR(${_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt}, 'YYYY-MM')`).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.sql)`TO_CHAR(${_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt}, 'YYYY-MM')`);\n        // Recent activity (last 7 days)\n        const recentActivity = [];\n        for(let i = 6; i >= 0; i--){\n            const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);\n            const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n            const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);\n            const candidatesCount = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, startOfDay), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt, endOfDay)));\n            const resultsCount = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, startOfDay), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.lte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt, endOfDay)));\n            recentActivity.push({\n                date: startOfDay.toISOString().split('T')[0],\n                candidates: candidatesCount[0]?.count || 0,\n                results: resultsCount[0]?.count || 0\n            });\n        }\n        const reportData = {\n            overview: {\n                totalCandidates: totalCandidatesResult[0]?.count || 0,\n                totalResults: totalResultsResult[0]?.count || 0,\n                averageBandScore: Number(averageBandScoreResult[0]?.avg) || 0,\n                certificatesGenerated: certificatesGeneratedResult[0]?.count || 0\n            },\n            monthlyStats: monthlyStats.map((stat)=>({\n                    month: stat.month,\n                    candidates: stat.candidates,\n                    results: stat.results,\n                    avgScore: Number(stat.avgScore) || 0\n                })),\n            bandScoreDistribution: processedBandDistribution,\n            testCenterStats: testCenterStats.map((stat)=>({\n                    center: stat.center,\n                    candidates: stat.candidates,\n                    avgScore: Number(stat.avgScore) || 0\n                })),\n            recentActivity\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(reportData);\n    } catch (error) {\n        console.error('Reports error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate reports'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3NjaGVtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0c7QUFDaEQ7QUFFaEQsaUNBQWlDO0FBQzFCLE1BQU1RLFFBQVFSLDREQUFPQSxDQUFDLFNBQVM7SUFDcENTLElBQUlSLHlEQUFJQSxDQUFDLE1BQU1TLFVBQVUsR0FBR0MsVUFBVSxDQUFDLElBQU1KLDhEQUFRQTtJQUNyREssTUFBTVgseURBQUlBLENBQUM7SUFDWFksT0FBT1oseURBQUlBLENBQUMsU0FBU2EsT0FBTyxHQUFHQyxNQUFNO0lBQ3JDQyxlQUFlZCw4REFBU0EsQ0FBQyxpQkFBaUI7UUFBRWUsTUFBTTtJQUFPO0lBQ3pEQyxPQUFPakIseURBQUlBLENBQUM7SUFDWmtCLFVBQVVsQix5REFBSUEsQ0FBQztJQUNmbUIsTUFBTW5CLHlEQUFJQSxDQUFDLFFBQVE7UUFBRW9CLE1BQU07WUFBQztZQUFTO1NBQWU7SUFBQyxHQUFHUCxPQUFPLEdBQUdRLE9BQU8sQ0FBQztJQUMxRUMsV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCwyQkFBMkI7QUFDcEIsTUFBTVksV0FBVzFCLDREQUFPQSxDQUFDLFlBQVk7SUFDMUMyQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGQyxNQUFNN0IseURBQUlBLENBQUMsUUFBUWEsT0FBTztJQUMxQmlCLFVBQVU5Qix5REFBSUEsQ0FBQyxZQUFZYSxPQUFPO0lBQ2xDa0IsbUJBQW1CL0IseURBQUlBLENBQUMscUJBQXFCYSxPQUFPO0lBQ3BEbUIsZUFBZWhDLHlEQUFJQSxDQUFDO0lBQ3BCaUMsY0FBY2pDLHlEQUFJQSxDQUFDO0lBQ25Ca0MsWUFBWWhDLDREQUFPQSxDQUFDO0lBQ3BCaUMsWUFBWW5DLHlEQUFJQSxDQUFDO0lBQ2pCb0MsT0FBT3BDLHlEQUFJQSxDQUFDO0lBQ1pxQyxVQUFVckMseURBQUlBLENBQUM7SUFDZnNDLGVBQWV0Qyx5REFBSUEsQ0FBQztBQUN0QixHQUFHO0FBRUgsb0NBQW9DO0FBQzdCLE1BQU11QyxXQUFXeEMsNERBQU9BLENBQUMsWUFBWTtJQUMxQ3lDLGNBQWN4Qyx5REFBSUEsQ0FBQyxnQkFBZ0JTLFVBQVU7SUFDN0NpQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGYSxTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsc0JBQXNCO0FBQ2YsTUFBTTZCLHFCQUFxQjNDLDREQUFPQSxDQUFDLHNCQUFzQjtJQUM5RDRDLFlBQVkzQyx5REFBSUEsQ0FBQyxjQUFjYSxPQUFPO0lBQ3RDK0IsT0FBTzVDLHlEQUFJQSxDQUFDLFNBQVNhLE9BQU87SUFDNUI0QixTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsbUJBQW1CO0FBQ1osTUFBTWdDLGFBQWE5Qyw0REFBT0EsQ0FBQyxjQUFjO0lBQzlDUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckR3QyxpQkFBaUI5Qyx5REFBSUEsQ0FBQyxvQkFBb0JhLE9BQU8sR0FBR0MsTUFBTTtJQUMxRGlDLFVBQVUvQyx5REFBSUEsQ0FBQyxhQUFhYSxPQUFPO0lBQ25DRCxPQUFPWix5REFBSUEsQ0FBQyxTQUFTYSxPQUFPLEdBQUdDLE1BQU07SUFDckNrQyxhQUFhaEQseURBQUlBLENBQUMsZ0JBQWdCYSxPQUFPO0lBQ3pDb0MsYUFBYWhELDhEQUFTQSxDQUFDLGlCQUFpQjtRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztJQUNqRXFDLGFBQWFsRCx5REFBSUEsQ0FBQyxlQUFlYSxPQUFPO0lBQ3hDc0MsZ0JBQWdCbkQseURBQUlBLENBQUMsbUJBQW1CYSxPQUFPLEdBQUdDLE1BQU07SUFDeERzQyxVQUFVbkQsOERBQVNBLENBQUMsYUFBYTtRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztJQUMxRHdDLFlBQVlyRCx5REFBSUEsQ0FBQyxlQUFlYSxPQUFPO0lBQ3ZDeUMsVUFBVXRELHlEQUFJQSxDQUFDO0lBQ2Z1RCxXQUFXdkQseURBQUlBLENBQUM7SUFDaEJzQixXQUFXckIsOERBQVNBLENBQUMsY0FBY3NCLFVBQVUsR0FBR1YsT0FBTztJQUN2RFcsV0FBV3ZCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87QUFDekQsR0FBRztBQUVILHFCQUFxQjtBQUNkLE1BQU0yQyxjQUFjekQsNERBQU9BLENBQUMsZ0JBQWdCO0lBQ2pEUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckRtRCxhQUFhekQseURBQUlBLENBQUMsZ0JBQWdCYSxPQUFPLEdBQUdjLFVBQVUsQ0FBQyxJQUFNa0IsV0FBV3JDLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBRWxHLG1CQUFtQjtJQUNuQjhCLGdCQUFnQnZELDREQUFPQSxDQUFDLG1CQUFtQjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDcEVDLG9CQUFvQjFELDREQUFPQSxDQUFDLHdCQUF3QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFN0UsaUJBQWlCO0lBQ2pCRSxjQUFjM0QsNERBQU9BLENBQUMsaUJBQWlCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUNoRUcsa0JBQWtCNUQsNERBQU9BLENBQUMsc0JBQXNCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUV6RSxpQkFBaUI7SUFDakJJLG1CQUFtQjdELDREQUFPQSxDQUFDLHVCQUF1QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDM0VLLG1CQUFtQjlELDREQUFPQSxDQUFDLHVCQUF1QjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDM0VNLGtCQUFrQi9ELDREQUFPQSxDQUFDLHNCQUFzQjtRQUFFd0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFekUsa0JBQWtCO0lBQ2xCTyxzQkFBc0JoRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGUSxzQkFBc0JqRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGUyxzQkFBc0JsRSw0REFBT0EsQ0FBQywwQkFBMEI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ2pGVSw0QkFBNEJuRSw0REFBT0EsQ0FBQyxnQ0FBZ0M7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQzdGVyxtQkFBbUJwRSw0REFBT0EsQ0FBQyx1QkFBdUI7UUFBRXdELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBRTNFLGdCQUFnQjtJQUNoQlksa0JBQWtCckUsNERBQU9BLENBQUMsc0JBQXNCO1FBQUV3RCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUV6RSxzQkFBc0I7SUFDdEJhLFFBQVF6RSx5REFBSUEsQ0FBQyxVQUFVO1FBQUVvQixNQUFNO1lBQUM7WUFBVztZQUFhO1NBQVc7SUFBQyxHQUFHUCxPQUFPLEdBQUdRLE9BQU8sQ0FBQztJQUN6RnFELFdBQVcxRSx5REFBSUEsQ0FBQyxjQUFjMkIsVUFBVSxDQUFDLElBQU1wQixNQUFNQyxFQUFFO0lBQ3ZEbUUsWUFBWTNFLHlEQUFJQSxDQUFDLGVBQWUyQixVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUU7SUFDekRvRSxzQkFBc0J4RSw0REFBT0EsQ0FBQyx5QkFBeUJpQixPQUFPLENBQUM7SUFDL0R3RCxtQkFBbUI3RSx5REFBSUEsQ0FBQyxzQkFBc0JjLE1BQU07SUFDcERnRSxnQkFBZ0I5RSx5REFBSUEsQ0FBQztJQUNyQitFLHFCQUFxQjNFLDREQUFPQSxDQUFDLHlCQUF5QmlCLE9BQU8sQ0FBQztJQUM5RCtCLFVBQVVuRCw4REFBU0EsQ0FBQyxhQUFhO1FBQUVlLE1BQU07SUFBTztJQUVoRE0sV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCxvQkFBb0I7QUFDYixNQUFNbUUsYUFBYWpGLDREQUFPQSxDQUFDLGVBQWU7SUFDL0NTLElBQUlSLHlEQUFJQSxDQUFDLE1BQU1TLFVBQVUsR0FBR0MsVUFBVSxDQUFDLElBQU1KLDhEQUFRQTtJQUNyRDJFLGNBQWNqRix5REFBSUEsQ0FBQyxrQkFBa0JhLE9BQU8sR0FBR2MsVUFBVSxDQUFDLElBQU02QixZQUFZaEQsRUFBRSxFQUFFO1FBQUVvQixVQUFVO0lBQVU7SUFFdEcsMEJBQTBCO0lBQzFCc0QsbUJBQW1CbEYseURBQUlBLENBQUM7SUFDeEJtRixpQkFBaUJuRix5REFBSUEsQ0FBQztJQUN0Qm9GLGlCQUFpQnBGLHlEQUFJQSxDQUFDO0lBQ3RCcUYsa0JBQWtCckYseURBQUlBLENBQUM7SUFFdkIsdUNBQXVDO0lBQ3ZDc0YsaUJBQWlCdEYseURBQUlBLENBQUM7SUFDdEJ1RixzQkFBc0J2Rix5REFBSUEsQ0FBQztJQUUzQiwyQkFBMkI7SUFDM0J3RixXQUFXbkYseURBQUlBLENBQUMsYUFBYW9GLEtBQUs7SUFDbENDLFlBQVlyRix5REFBSUEsQ0FBQyxjQUFjb0YsS0FBSztJQUVwQyx5QkFBeUI7SUFDekJFLFdBQVd0Rix5REFBSUEsQ0FBQyxjQUFjb0YsS0FBSztJQU9uQ0csYUFBYTNGLDhEQUFTQSxDQUFDLGdCQUFnQnNCLFVBQVUsR0FBR1YsT0FBTztBQUM3RCxHQUFHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbVxcc3JjXFxsaWJcXGRiXFxzY2hlbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGdUYWJsZSwgdGV4dCwgdGltZXN0YW1wLCBpbnRlZ2VyLCBkZWNpbWFsLCBib29sZWFuLCBqc29uIH0gZnJvbSAnZHJpenpsZS1vcm0vcGctY29yZSc7XHJcbmltcG9ydCB7IGNyZWF0ZUlkIH0gZnJvbSAnQHBhcmFsbGVsZHJpdmUvY3VpZDInO1xyXG5cclxuLy8gVXNlcnMgdGFibGUgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBjb25zdCB1c2VycyA9IHBnVGFibGUoJ3VzZXJzJywge1xyXG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxyXG4gIG5hbWU6IHRleHQoJ25hbWUnKSxcclxuICBlbWFpbDogdGV4dCgnZW1haWwnKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgZW1haWxWZXJpZmllZDogdGltZXN0YW1wKCdlbWFpbFZlcmlmaWVkJywgeyBtb2RlOiAnZGF0ZScgfSksXHJcbiAgaW1hZ2U6IHRleHQoJ2ltYWdlJyksXHJcbiAgcGFzc3dvcmQ6IHRleHQoJ3Bhc3N3b3JkJyksIC8vIEFkZGVkIHBhc3N3b3JkIGZpZWxkIGZvciBhdXRoZW50aWNhdGlvblxyXG4gIHJvbGU6IHRleHQoJ3JvbGUnLCB7IGVudW06IFsnYWRtaW4nLCAndGVzdF9jaGVja2VyJ10gfSkubm90TnVsbCgpLmRlZmF1bHQoJ3Rlc3RfY2hlY2tlcicpLFxyXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcclxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gQWNjb3VudHMgdGFibGUgZm9yIE9BdXRoXHJcbmV4cG9ydCBjb25zdCBhY2NvdW50cyA9IHBnVGFibGUoJ2FjY291bnRzJywge1xyXG4gIHVzZXJJZDogdGV4dCgndXNlcklkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuICB0eXBlOiB0ZXh0KCd0eXBlJykubm90TnVsbCgpLFxyXG4gIHByb3ZpZGVyOiB0ZXh0KCdwcm92aWRlcicpLm5vdE51bGwoKSxcclxuICBwcm92aWRlckFjY291bnRJZDogdGV4dCgncHJvdmlkZXJBY2NvdW50SWQnKS5ub3ROdWxsKCksXHJcbiAgcmVmcmVzaF90b2tlbjogdGV4dCgncmVmcmVzaF90b2tlbicpLFxyXG4gIGFjY2Vzc190b2tlbjogdGV4dCgnYWNjZXNzX3Rva2VuJyksXHJcbiAgZXhwaXJlc19hdDogaW50ZWdlcignZXhwaXJlc19hdCcpLFxyXG4gIHRva2VuX3R5cGU6IHRleHQoJ3Rva2VuX3R5cGUnKSxcclxuICBzY29wZTogdGV4dCgnc2NvcGUnKSxcclxuICBpZF90b2tlbjogdGV4dCgnaWRfdG9rZW4nKSxcclxuICBzZXNzaW9uX3N0YXRlOiB0ZXh0KCdzZXNzaW9uX3N0YXRlJyksXHJcbn0pO1xyXG5cclxuLy8gU2Vzc2lvbnMgdGFibGUgZm9yIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBjb25zdCBzZXNzaW9ucyA9IHBnVGFibGUoJ3Nlc3Npb25zJywge1xyXG4gIHNlc3Npb25Ub2tlbjogdGV4dCgnc2Vzc2lvblRva2VuJykucHJpbWFyeUtleSgpLFxyXG4gIHVzZXJJZDogdGV4dCgndXNlcklkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuICBleHBpcmVzOiB0aW1lc3RhbXAoJ2V4cGlyZXMnLCB7IG1vZGU6ICdkYXRlJyB9KS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gVmVyaWZpY2F0aW9uIHRva2Vuc1xyXG5leHBvcnQgY29uc3QgdmVyaWZpY2F0aW9uVG9rZW5zID0gcGdUYWJsZSgndmVyaWZpY2F0aW9uVG9rZW5zJywge1xyXG4gIGlkZW50aWZpZXI6IHRleHQoJ2lkZW50aWZpZXInKS5ub3ROdWxsKCksXHJcbiAgdG9rZW46IHRleHQoJ3Rva2VuJykubm90TnVsbCgpLFxyXG4gIGV4cGlyZXM6IHRpbWVzdGFtcCgnZXhwaXJlcycsIHsgbW9kZTogJ2RhdGUnIH0pLm5vdE51bGwoKSxcclxufSk7XHJcblxyXG4vLyBDYW5kaWRhdGVzIHRhYmxlXHJcbmV4cG9ydCBjb25zdCBjYW5kaWRhdGVzID0gcGdUYWJsZSgnY2FuZGlkYXRlcycsIHtcclxuICBpZDogdGV4dCgnaWQnKS5wcmltYXJ5S2V5KCkuJGRlZmF1bHRGbigoKSA9PiBjcmVhdGVJZCgpKSxcclxuICBjYW5kaWRhdGVOdW1iZXI6IHRleHQoJ2NhbmRpZGF0ZV9udW1iZXInKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgZnVsbE5hbWU6IHRleHQoJ2Z1bGxfbmFtZScpLm5vdE51bGwoKSxcclxuICBlbWFpbDogdGV4dCgnZW1haWwnKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgcGhvbmVOdW1iZXI6IHRleHQoJ3Bob25lX251bWJlcicpLm5vdE51bGwoKSxcclxuICBkYXRlT2ZCaXJ0aDogdGltZXN0YW1wKCdkYXRlX29mX2JpcnRoJywgeyBtb2RlOiAnZGF0ZScgfSkubm90TnVsbCgpLFxyXG4gIG5hdGlvbmFsaXR5OiB0ZXh0KCduYXRpb25hbGl0eScpLm5vdE51bGwoKSxcclxuICBwYXNzcG9ydE51bWJlcjogdGV4dCgncGFzc3BvcnRfbnVtYmVyJykubm90TnVsbCgpLnVuaXF1ZSgpLFxyXG4gIHRlc3REYXRlOiB0aW1lc3RhbXAoJ3Rlc3RfZGF0ZScsIHsgbW9kZTogJ2RhdGUnIH0pLm5vdE51bGwoKSxcclxuICB0ZXN0Q2VudGVyOiB0ZXh0KCd0ZXN0X2NlbnRlcicpLm5vdE51bGwoKSxcclxuICBwaG90b1VybDogdGV4dCgncGhvdG9fdXJsJyksXHJcbiAgcGhvdG9EYXRhOiB0ZXh0KCdwaG90b19kYXRhJyksIC8vIEJhc2U2NCBlbmNvZGVkIHBob3RvIGRhdGFcclxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcCgnY3JlYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoJ3VwZGF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxyXG59KTtcclxuXHJcbi8vIFRlc3QgcmVzdWx0cyB0YWJsZVxyXG5leHBvcnQgY29uc3QgdGVzdFJlc3VsdHMgPSBwZ1RhYmxlKCd0ZXN0X3Jlc3VsdHMnLCB7XHJcbiAgaWQ6IHRleHQoJ2lkJykucHJpbWFyeUtleSgpLiRkZWZhdWx0Rm4oKCkgPT4gY3JlYXRlSWQoKSksXHJcbiAgY2FuZGlkYXRlSWQ6IHRleHQoJ2NhbmRpZGF0ZV9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IGNhbmRpZGF0ZXMuaWQsIHsgb25EZWxldGU6ICdjYXNjYWRlJyB9KSxcclxuXHJcbiAgLy8gTGlzdGVuaW5nIHNjb3Jlc1xyXG4gIGxpc3RlbmluZ1Njb3JlOiBkZWNpbWFsKCdsaXN0ZW5pbmdfc2NvcmUnLCB7IHByZWNpc2lvbjogMywgc2NhbGU6IDEgfSksXHJcbiAgbGlzdGVuaW5nQmFuZFNjb3JlOiBkZWNpbWFsKCdsaXN0ZW5pbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcclxuXHJcbiAgLy8gUmVhZGluZyBzY29yZXNcclxuICByZWFkaW5nU2NvcmU6IGRlY2ltYWwoJ3JlYWRpbmdfc2NvcmUnLCB7IHByZWNpc2lvbjogMywgc2NhbGU6IDEgfSksXHJcbiAgcmVhZGluZ0JhbmRTY29yZTogZGVjaW1hbCgncmVhZGluZ19iYW5kX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG5cclxuICAvLyBXcml0aW5nIHNjb3Jlc1xyXG4gIHdyaXRpbmdUYXNrMVNjb3JlOiBkZWNpbWFsKCd3cml0aW5nX3Rhc2sxX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG4gIHdyaXRpbmdUYXNrMlNjb3JlOiBkZWNpbWFsKCd3cml0aW5nX3Rhc2syX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxyXG4gIHdyaXRpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3dyaXRpbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcclxuXHJcbiAgLy8gU3BlYWtpbmcgc2NvcmVzXHJcbiAgc3BlYWtpbmdGbHVlbmN5U2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2ZsdWVuY3lfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdMZXhpY2FsU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2xleGljYWxfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdHcmFtbWFyU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2dyYW1tYXJfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdQcm9udW5jaWF0aW9uU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX3Byb251bmNpYXRpb25fc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcbiAgc3BlYWtpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2JhbmRfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcblxyXG4gIC8vIE92ZXJhbGwgc2NvcmVcclxuICBvdmVyYWxsQmFuZFNjb3JlOiBkZWNpbWFsKCdvdmVyYWxsX2JhbmRfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXHJcblxyXG4gIC8vIFN0YXR1cyBhbmQgbWV0YWRhdGFcclxuICBzdGF0dXM6IHRleHQoJ3N0YXR1cycsIHsgZW51bTogWydwZW5kaW5nJywgJ2NvbXBsZXRlZCcsICd2ZXJpZmllZCddIH0pLm5vdE51bGwoKS5kZWZhdWx0KCdwZW5kaW5nJyksXHJcbiAgZW50ZXJlZEJ5OiB0ZXh0KCdlbnRlcmVkX2J5JykucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCksXHJcbiAgdmVyaWZpZWRCeTogdGV4dCgndmVyaWZpZWRfYnknKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcclxuICBjZXJ0aWZpY2F0ZUdlbmVyYXRlZDogYm9vbGVhbignY2VydGlmaWNhdGVfZ2VuZXJhdGVkJykuZGVmYXVsdChmYWxzZSksXHJcbiAgY2VydGlmaWNhdGVTZXJpYWw6IHRleHQoJ2NlcnRpZmljYXRlX3NlcmlhbCcpLnVuaXF1ZSgpLFxyXG4gIGNlcnRpZmljYXRlVXJsOiB0ZXh0KCdjZXJ0aWZpY2F0ZV91cmwnKSxcclxuICBhaUZlZWRiYWNrR2VuZXJhdGVkOiBib29sZWFuKCdhaV9mZWVkYmFja19nZW5lcmF0ZWQnKS5kZWZhdWx0KGZhbHNlKSxcclxuICB0ZXN0RGF0ZTogdGltZXN0YW1wKCd0ZXN0X2RhdGUnLCB7IG1vZGU6ICdkYXRlJyB9KSxcclxuXHJcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxyXG4gIHVwZGF0ZWRBdDogdGltZXN0YW1wKCd1cGRhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcclxufSk7XHJcblxyXG4vLyBBSSBmZWVkYmFjayB0YWJsZVxyXG5leHBvcnQgY29uc3QgYWlGZWVkYmFjayA9IHBnVGFibGUoJ2FpX2ZlZWRiYWNrJywge1xyXG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxyXG4gIHRlc3RSZXN1bHRJZDogdGV4dCgndGVzdF9yZXN1bHRfaWQnKS5ub3ROdWxsKCkucmVmZXJlbmNlcygoKSA9PiB0ZXN0UmVzdWx0cy5pZCwgeyBvbkRlbGV0ZTogJ2Nhc2NhZGUnIH0pLFxyXG5cclxuICAvLyBGZWVkYmFjayBmb3IgZWFjaCBza2lsbFxyXG4gIGxpc3RlbmluZ0ZlZWRiYWNrOiB0ZXh0KCdsaXN0ZW5pbmdfZmVlZGJhY2snKSxcclxuICByZWFkaW5nRmVlZGJhY2s6IHRleHQoJ3JlYWRpbmdfZmVlZGJhY2snKSxcclxuICB3cml0aW5nRmVlZGJhY2s6IHRleHQoJ3dyaXRpbmdfZmVlZGJhY2snKSxcclxuICBzcGVha2luZ0ZlZWRiYWNrOiB0ZXh0KCdzcGVha2luZ19mZWVkYmFjaycpLFxyXG5cclxuICAvLyBPdmVyYWxsIGZlZWRiYWNrIGFuZCByZWNvbW1lbmRhdGlvbnNcclxuICBvdmVyYWxsRmVlZGJhY2s6IHRleHQoJ292ZXJhbGxfZmVlZGJhY2snKSxcclxuICBzdHVkeVJlY29tbWVuZGF0aW9uczogdGV4dCgnc3R1ZHlfcmVjb21tZW5kYXRpb25zJyksXHJcblxyXG4gIC8vIFN0cmVuZ3RocyBhbmQgd2Vha25lc3Nlc1xyXG4gIHN0cmVuZ3RoczoganNvbignc3RyZW5ndGhzJykuJHR5cGU8c3RyaW5nW10+KCksXHJcbiAgd2Vha25lc3NlczoganNvbignd2Vha25lc3NlcycpLiR0eXBlPHN0cmluZ1tdPigpLFxyXG5cclxuICAvLyBTdHVkeSBwbGFuIHN1Z2dlc3Rpb25zXHJcbiAgc3R1ZHlQbGFuOiBqc29uKCdzdHVkeV9wbGFuJykuJHR5cGU8e1xyXG4gICAgdGltZWZyYW1lOiBzdHJpbmc7XHJcbiAgICBmb2N1c0FyZWFzOiBzdHJpbmdbXTtcclxuICAgIHJlc291cmNlczogc3RyaW5nW107XHJcbiAgICBwcmFjdGljZUFjdGl2aXRpZXM6IHN0cmluZ1tdO1xyXG4gIH0+KCksXHJcblxyXG4gIGdlbmVyYXRlZEF0OiB0aW1lc3RhbXAoJ2dlbmVyYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuLy8gRXhwb3J0IHR5cGVzXHJcbmV4cG9ydCB0eXBlIFVzZXIgPSB0eXBlb2YgdXNlcnMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdVc2VyID0gdHlwZW9mIHVzZXJzLiRpbmZlckluc2VydDtcclxuZXhwb3J0IHR5cGUgQ2FuZGlkYXRlID0gdHlwZW9mIGNhbmRpZGF0ZXMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdDYW5kaWRhdGUgPSB0eXBlb2YgY2FuZGlkYXRlcy4kaW5mZXJJbnNlcnQ7XHJcbmV4cG9ydCB0eXBlIFRlc3RSZXN1bHQgPSB0eXBlb2YgdGVzdFJlc3VsdHMuJGluZmVyU2VsZWN0O1xyXG5leHBvcnQgdHlwZSBOZXdUZXN0UmVzdWx0ID0gdHlwZW9mIHRlc3RSZXN1bHRzLiRpbmZlckluc2VydDtcclxuZXhwb3J0IHR5cGUgQUlGZWVkYmFjayA9IHR5cGVvZiBhaUZlZWRiYWNrLiRpbmZlclNlbGVjdDtcclxuZXhwb3J0IHR5cGUgTmV3QUlGZWVkYmFjayA9IHR5cGVvZiBhaUZlZWRiYWNrLiRpbmZlckluc2VydDtcclxuIl0sIm5hbWVzIjpbInBnVGFibGUiLCJ0ZXh0IiwidGltZXN0YW1wIiwiaW50ZWdlciIsImRlY2ltYWwiLCJib29sZWFuIiwianNvbiIsImNyZWF0ZUlkIiwidXNlcnMiLCJpZCIsInByaW1hcnlLZXkiLCIkZGVmYXVsdEZuIiwibmFtZSIsImVtYWlsIiwibm90TnVsbCIsInVuaXF1ZSIsImVtYWlsVmVyaWZpZWQiLCJtb2RlIiwiaW1hZ2UiLCJwYXNzd29yZCIsInJvbGUiLCJlbnVtIiwiZGVmYXVsdCIsImNyZWF0ZWRBdCIsImRlZmF1bHROb3ciLCJ1cGRhdGVkQXQiLCJhY2NvdW50cyIsInVzZXJJZCIsInJlZmVyZW5jZXMiLCJvbkRlbGV0ZSIsInR5cGUiLCJwcm92aWRlciIsInByb3ZpZGVyQWNjb3VudElkIiwicmVmcmVzaF90b2tlbiIsImFjY2Vzc190b2tlbiIsImV4cGlyZXNfYXQiLCJ0b2tlbl90eXBlIiwic2NvcGUiLCJpZF90b2tlbiIsInNlc3Npb25fc3RhdGUiLCJzZXNzaW9ucyIsInNlc3Npb25Ub2tlbiIsImV4cGlyZXMiLCJ2ZXJpZmljYXRpb25Ub2tlbnMiLCJpZGVudGlmaWVyIiwidG9rZW4iLCJjYW5kaWRhdGVzIiwiY2FuZGlkYXRlTnVtYmVyIiwiZnVsbE5hbWUiLCJwaG9uZU51bWJlciIsImRhdGVPZkJpcnRoIiwibmF0aW9uYWxpdHkiLCJwYXNzcG9ydE51bWJlciIsInRlc3REYXRlIiwidGVzdENlbnRlciIsInBob3RvVXJsIiwicGhvdG9EYXRhIiwidGVzdFJlc3VsdHMiLCJjYW5kaWRhdGVJZCIsImxpc3RlbmluZ1Njb3JlIiwicHJlY2lzaW9uIiwic2NhbGUiLCJsaXN0ZW5pbmdCYW5kU2NvcmUiLCJyZWFkaW5nU2NvcmUiLCJyZWFkaW5nQmFuZFNjb3JlIiwid3JpdGluZ1Rhc2sxU2NvcmUiLCJ3cml0aW5nVGFzazJTY29yZSIsIndyaXRpbmdCYW5kU2NvcmUiLCJzcGVha2luZ0ZsdWVuY3lTY29yZSIsInNwZWFraW5nTGV4aWNhbFNjb3JlIiwic3BlYWtpbmdHcmFtbWFyU2NvcmUiLCJzcGVha2luZ1Byb251bmNpYXRpb25TY29yZSIsInNwZWFraW5nQmFuZFNjb3JlIiwib3ZlcmFsbEJhbmRTY29yZSIsInN0YXR1cyIsImVudGVyZWRCeSIsInZlcmlmaWVkQnkiLCJjZXJ0aWZpY2F0ZUdlbmVyYXRlZCIsImNlcnRpZmljYXRlU2VyaWFsIiwiY2VydGlmaWNhdGVVcmwiLCJhaUZlZWRiYWNrR2VuZXJhdGVkIiwiYWlGZWVkYmFjayIsInRlc3RSZXN1bHRJZCIsImxpc3RlbmluZ0ZlZWRiYWNrIiwicmVhZGluZ0ZlZWRiYWNrIiwid3JpdGluZ0ZlZWRiYWNrIiwic3BlYWtpbmdGZWVkYmFjayIsIm92ZXJhbGxGZWVkYmFjayIsInN0dWR5UmVjb21tZW5kYXRpb25zIiwic3RyZW5ndGhzIiwiJHR5cGUiLCJ3ZWFrbmVzc2VzIiwic3R1ZHlQbGFuIiwiZ2VuZXJhdGVkQXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Freports%2Froute&page=%2Fapi%2Fadmin%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Freports%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();