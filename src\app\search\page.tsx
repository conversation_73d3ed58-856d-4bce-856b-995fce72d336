'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, FileText, ArrowLeft, Eye, User } from 'lucide-react';

interface SearchResult {
  id: string;
  listeningBandScore?: number;
  readingBandScore?: number;
  writingBandScore?: number;
  speakingBandScore?: number;
  overallBandScore?: number;
  certificateGenerated: boolean;
  candidate: {
    fullName: string;
    testDate: string;
    testCenter: string;
  };
}

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setHasSearched(true);

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
        }),
      });

      if (response.ok) {
        const results = await response.json();
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back
              </Link>
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Search IELTS Results</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search Form */}
        <div className="bg-white rounded-xl shadow-xl p-8 mb-8 border border-gray-100">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Search className="h-8 w-8 text-blue-600" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Find Your IELTS Results</h2>
            <p className="text-gray-600">Enter any of the following: Full name, Email, Passport number, Candidate number, or Result ID</p>
          </div>

          <form onSubmit={handleSearch} className="space-y-6">
            <div className="relative">
              <label htmlFor="searchQuery" className="block text-sm font-medium text-gray-700 mb-3">
                Search for your test results
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="searchQuery"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Enter your name, email, passport number, candidate number, or result ID"
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  required
                />
              </div>
              <div className="mt-2 text-xs text-gray-500">
                <span className="font-medium">Examples:</span> John Smith, <EMAIL>, A12345678, 001, or result-id-123
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center items-center px-6 py-4 border border-transparent text-lg font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-5 w-5 mr-3" />
                  Search Results
                </>
              )}
            </button>
          </form>
        </div>

        {/* Search Results */}
        {hasSearched && (
          <div className="bg-white rounded-xl shadow-xl p-8 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Search Results</h3>
              {searchResults.length > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </span>
              )}
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-lg text-gray-600">Searching for your results...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-6">
                {searchResults.map((result) => (
                  <div key={result.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200 bg-gradient-to-r from-gray-50 to-white">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center mb-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                            <User className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="text-xl font-bold text-gray-900">{result.candidate.fullName}</h4>
                            <p className="text-gray-600">Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}</p>
                          </div>
                        </div>

                        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600 mb-1">
                            <span className="font-medium">Test Center:</span> {result.candidate.testCenter}
                          </p>
                        </div>

                        {/* Module Scores */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          {result.listeningBandScore && (
                            <div className="text-center p-3 bg-blue-50 rounded-lg">
                              <p className="text-sm font-medium text-blue-800">Listening</p>
                              <p className="text-2xl font-bold text-blue-600">{result.listeningBandScore}</p>
                            </div>
                          )}
                          {result.readingBandScore && (
                            <div className="text-center p-3 bg-green-50 rounded-lg">
                              <p className="text-sm font-medium text-green-800">Reading</p>
                              <p className="text-2xl font-bold text-green-600">{result.readingBandScore}</p>
                            </div>
                          )}
                          {result.writingBandScore && (
                            <div className="text-center p-3 bg-yellow-50 rounded-lg">
                              <p className="text-sm font-medium text-yellow-800">Writing</p>
                              <p className="text-2xl font-bold text-yellow-600">{result.writingBandScore}</p>
                            </div>
                          )}
                          {result.speakingBandScore && (
                            <div className="text-center p-3 bg-purple-50 rounded-lg">
                              <p className="text-sm font-medium text-purple-800">Speaking</p>
                              <p className="text-2xl font-bold text-purple-600">{result.speakingBandScore}</p>
                            </div>
                          )}
                        </div>

                        {/* Overall Score */}
                        {result.overallBandScore && (
                          <div className="text-center p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
                            <p className="text-sm font-medium text-gray-700 mb-1">Overall Band Score</p>
                            <p className="text-4xl font-bold text-indigo-600">{result.overallBandScore}</p>
                          </div>
                        )}
                      </div>

                      <div className="ml-6 flex flex-col space-y-3">
                        <Link
                          href={`/results/${result.id}`}
                          className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Detailed Results
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-12 w-12 text-gray-400" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">No Results Found</h4>
                <p className="text-gray-600 mb-4">We couldn't find any test results matching your search criteria.</p>
                <div className="text-sm text-gray-500">
                  <p className="mb-2">Please check that you've entered:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Your full name exactly as registered</li>
                    <li>The correct email address</li>
                    <li>Your passport number without spaces</li>
                    <li>Your candidate number (e.g., 001, 002)</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}
