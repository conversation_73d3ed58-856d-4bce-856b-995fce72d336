/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/checker/results/route";
exports.ids = ["app/api/checker/results/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/checker/results/route.ts */ \"(rsc)/./src/app/api/checker/results/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/checker/results/route\",\n        pathname: \"/api/checker/results\",\n        filename: \"route\",\n        bundlePath: \"app/api/checker/results/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\checker\\\\results\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/checker/results/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/checker/results/route.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n/* harmony import */ var _lib_certificate_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/certificate-generator */ \"(rsc)/./src/lib/certificate-generator.ts\");\n/* harmony import */ var _lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/certificate */ \"(rsc)/./src/lib/utils/certificate.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n// Helper function to auto-generate AI feedback and certificate\nasync function autoGenerateAIFeedbackAndCertificate(resultId) {\n    try {\n        console.log(`Auto-generating AI feedback and certificate for result: ${resultId}`);\n        // Get test result with candidate info\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            testResult: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults,\n            candidate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId)).limit(1);\n        if (!result.length) {\n            console.error('Test result not found for auto-generation:', resultId);\n            return;\n        }\n        const { testResult, candidate } = result[0];\n        // Generate AI Feedback if not already generated\n        if (!testResult.aiFeedbackGenerated) {\n            try {\n                console.log('Generating AI feedback...');\n                const feedbackData = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_4__.generateAIFeedback)(testResult, candidate);\n                // Save the feedback to database\n                await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.aiFeedback).values({\n                    testResultId: resultId,\n                    listeningFeedback: feedbackData.listeningFeedback,\n                    readingFeedback: feedbackData.readingFeedback,\n                    writingFeedback: feedbackData.writingFeedback,\n                    speakingFeedback: feedbackData.speakingFeedback,\n                    overallFeedback: feedbackData.overallFeedback,\n                    studyRecommendations: feedbackData.recommendations,\n                    strengths: feedbackData.strengths,\n                    weaknesses: feedbackData.weaknesses,\n                    studyPlan: feedbackData.studyPlan\n                });\n                console.log('AI feedback generated successfully');\n            } catch (error) {\n                console.error('Error generating AI feedback:', error);\n            }\n        }\n        // Generate Certificate if not already generated\n        if (!testResult.certificateGenerated) {\n            try {\n                console.log('Generating certificate...');\n                // Generate certificate serial if not exists\n                let certificateSerial = testResult.certificateSerial;\n                if (!certificateSerial) {\n                    certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__.generateCertificateSerial)();\n                }\n                // Generate the certificate PDF\n                await (0,_lib_certificate_generator__WEBPACK_IMPORTED_MODULE_5__.generateCertificate)(testResult, candidate);\n                console.log('Certificate generated successfully');\n            } catch (error) {\n                console.error('Error generating certificate:', error);\n            }\n        }\n        // Update the test result to mark both as generated\n        const updateData = {\n            updatedAt: new Date()\n        };\n        if (!testResult.aiFeedbackGenerated) {\n            updateData.aiFeedbackGenerated = true;\n        }\n        if (!testResult.certificateGenerated) {\n            updateData.certificateGenerated = true;\n            if (!testResult.certificateSerial) {\n                updateData.certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__.generateCertificateSerial)();\n            }\n        }\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set(updateData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId));\n        console.log('Auto-generation completed for result:', resultId);\n    } catch (error) {\n        console.error('Error in auto-generation process:', error);\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        if (!data.candidateId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if candidate exists\n        const candidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, data.candidateId)).limit(1);\n        if (!candidate.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate not found'\n            }, {\n                status: 404\n            });\n        }\n        // Check if results already exist for this candidate\n        const existingResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, data.candidateId)).limit(1);\n        const isUpdate = existingResults.length > 0;\n        // Convert string values to numbers where needed\n        const processedData = {\n            candidateId: data.candidateId,\n            // Listening scores (keep as strings for decimal fields)\n            listeningScore: data.listeningScore || null,\n            listeningBandScore: data.listeningBandScore || null,\n            // Reading scores\n            readingScore: data.readingScore || null,\n            readingBandScore: data.readingBandScore || null,\n            // Writing scores\n            writingTask1Score: data.writingTask1Score || null,\n            writingTask2Score: data.writingTask2Score || null,\n            writingBandScore: data.writingBandScore || null,\n            // Speaking scores\n            speakingFluencyScore: data.speakingFluencyScore || null,\n            speakingLexicalScore: data.speakingLexicalScore || null,\n            speakingGrammarScore: data.speakingGrammarScore || null,\n            speakingPronunciationScore: data.speakingPronunciationScore || null,\n            speakingBandScore: data.speakingBandScore || null,\n            // Overall score\n            overallBandScore: data.overallBandScore || null,\n            // Metadata\n            status: data.status || 'pending',\n            enteredBy: session.user?.id,\n            updatedAt: new Date()\n        };\n        let result;\n        if (isUpdate) {\n            // Update existing result\n            result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set(processedData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, existingResults[0].id)).returning();\n        } else {\n            // Create new test result\n            result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).values(processedData).returning();\n        }\n        const savedResult = result[0];\n        // Auto-generate AI feedback and certificate if result is completed\n        if (processedData.status === 'completed' && savedResult.overallBandScore) {\n            // Run in background without blocking the response\n            setImmediate(async ()=>{\n                try {\n                    await autoGenerateAIFeedbackAndCertificate(savedResult.id);\n                } catch (error) {\n                    console.error('Background generation failed:', error);\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(savedResult, {\n            status: isUpdate ? 200 : 201\n        });\n    } catch (error) {\n        console.error('Error creating test result:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const status = searchParams.get('status');\n        const offset = (page - 1) * limit;\n        const userId = session.user?.id;\n        // Build where conditions\n        let whereConditions = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId);\n        if (status) {\n            whereConditions = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId);\n        // Add status filter if needed\n        }\n        // Get results with candidate info\n        const results = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n            candidateId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId,\n            listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n            readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n            writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n            speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n            overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n            status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status,\n            createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt,\n            candidate: {\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testDate\n            }\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(whereConditions).limit(limit).offset(offset).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            results,\n            page,\n            limit\n        });\n    } catch (error) {\n        console.error('Error fetching test results:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/checker/results/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAIFeedback: () => (/* binding */ generateAIFeedback),\n/* harmony export */   generatePersonalizedFeedback: () => (/* binding */ generatePersonalizedFeedback)\n/* harmony export */ });\n/* harmony import */ var _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @anthropic-ai/sdk */ \"(rsc)/./node_modules/@anthropic-ai/sdk/index.mjs\");\n\nconst anthropic = new _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.ANTHROPIC_API_KEY\n});\nasync function generateAIFeedback(testResult, candidate) {\n    const prompt = `\nYou are an expert IELTS examiner and English language learning advisor. Please provide comprehensive feedback for the following IELTS test results:\n\nCandidate Information:\n- Name: ${candidate.fullName}\n- Nationality: ${candidate.nationality}\n- Test Date: ${candidate.testDate}\n\nTest Scores:\n- Listening: ${testResult.listeningBandScore}/9.0 (Raw: ${testResult.listeningScore})\n- Reading: ${testResult.readingBandScore}/9.0 (Raw: ${testResult.readingScore})\n- Writing: ${testResult.writingBandScore}/9.0 (Task 1: ${testResult.writingTask1Score}, Task 2: ${testResult.writingTask2Score})\n- Speaking: ${testResult.speakingBandScore}/9.0 (Fluency: ${testResult.speakingFluencyScore}, Lexical: ${testResult.speakingLexicalScore}, Grammar: ${testResult.speakingGrammarScore}, Pronunciation: ${testResult.speakingPronunciationScore})\n- Overall Band Score: ${testResult.overallBandScore}/9.0\n\nPlease provide:\n\n1. Detailed feedback for each skill (Listening, Reading, Writing, Speaking) - 2-3 sentences each\n2. Overall performance feedback - 3-4 sentences\n3. Specific recommendations for improvement - 4-5 actionable points\n4. List of 3-4 key strengths\n5. List of 3-4 areas for improvement\n6. A personalized study plan including:\n   - Recommended timeframe for improvement\n   - 3-4 focus areas\n   - 4-5 specific resources\n   - 5-6 practice activities\n\nFormat your response as JSON with the following structure:\n{\n  \"listeningFeedback\": \"...\",\n  \"readingFeedback\": \"...\",\n  \"writingFeedback\": \"...\",\n  \"speakingFeedback\": \"...\",\n  \"overallFeedback\": \"...\",\n  \"recommendations\": \"...\",\n  \"strengths\": [\"...\", \"...\", \"...\"],\n  \"weaknesses\": [\"...\", \"...\", \"...\"],\n  \"studyPlan\": {\n    \"timeframe\": \"...\",\n    \"focusAreas\": [\"...\", \"...\", \"...\"],\n    \"resources\": [\"...\", \"...\", \"...\", \"...\"],\n    \"practiceActivities\": [\"...\", \"...\", \"...\", \"...\", \"...\"]\n  }\n}\n\nMake the feedback constructive, specific, and encouraging. Focus on practical advice that the candidate can implement.\n`;\n    try {\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        const feedbackData = JSON.parse(content.text);\n        return feedbackData;\n    } catch (error) {\n        console.error('Error generating AI feedback:', error);\n        // Return fallback feedback if AI service fails\n        return {\n            listeningFeedback: `With a band score of ${testResult.listeningBandScore}, your listening skills show good comprehension abilities. Continue practicing with various accents and audio materials.`,\n            readingFeedback: `Your reading score of ${testResult.readingBandScore} indicates solid reading comprehension. Focus on improving speed and accuracy with different text types.`,\n            writingFeedback: `Your writing band score of ${testResult.writingBandScore} shows competent writing skills. Work on task achievement, coherence, and lexical resource.`,\n            speakingFeedback: `With a speaking score of ${testResult.speakingBandScore}, you demonstrate good oral communication. Continue practicing fluency and pronunciation.`,\n            overallFeedback: `Your overall band score of ${testResult.overallBandScore} reflects your current English proficiency level. With focused practice, you can improve your performance across all skills.`,\n            recommendations: 'Practice regularly with authentic IELTS materials, focus on your weaker skills, and consider taking a preparation course.',\n            strengths: [\n                'Good overall comprehension',\n                'Adequate vocabulary range',\n                'Basic grammar understanding'\n            ],\n            weaknesses: [\n                'Need more practice with complex structures',\n                'Improve accuracy',\n                'Enhance fluency'\n            ],\n            studyPlan: {\n                timeframe: '3-6 months of focused study',\n                focusAreas: [\n                    'Grammar accuracy',\n                    'Vocabulary expansion',\n                    'Test strategies'\n                ],\n                resources: [\n                    'Official IELTS materials',\n                    'Cambridge IELTS books',\n                    'Online practice tests',\n                    'English news websites'\n                ],\n                practiceActivities: [\n                    'Daily reading practice',\n                    'Speaking with native speakers',\n                    'Writing essays weekly',\n                    'Listening to podcasts',\n                    'Taking mock tests'\n                ]\n            }\n        };\n    }\n}\n// New function for the feedback API\nasync function generatePersonalizedFeedback(request) {\n    const { overallScore, listeningScore, readingScore, writingScore, speakingScore } = request;\n    // Check if Claude API key is available\n    const apiKey = process.env.ANTHROPIC_API_KEY;\n    if (!apiKey) {\n        console.warn('ANTHROPIC_API_KEY not found, using mock feedback');\n        return generateMockFeedback(request);\n    }\n    try {\n        // Prepare the prompt for Claude\n        const prompt = `You are an expert IELTS examiner and English language teacher. Generate personalized feedback for a student based on their IELTS test scores.\n\nTest Scores:\n- Overall Band Score: ${overallScore}\n- Listening: ${listeningScore || 'Not provided'}\n- Reading: ${readingScore || 'Not provided'}\n- Writing: ${writingScore || 'Not provided'}\n- Speaking: ${speakingScore || 'Not provided'}\n\nPlease provide detailed feedback in the following JSON format:\n{\n  \"overallAssessment\": \"A comprehensive assessment of the student's English proficiency level\",\n  \"strengths\": [\"List of 3-4 specific strengths based on the scores\"],\n  \"areasForImprovement\": [\"List of 3-4 specific areas that need improvement\"],\n  \"specificRecommendations\": {\n    \"listening\": \"Specific advice for improving listening skills\",\n    \"reading\": \"Specific advice for improving reading skills\",\n    \"writing\": \"Specific advice for improving writing skills\",\n    \"speaking\": \"Specific advice for improving speaking skills\"\n  },\n  \"studyPlan\": \"A detailed study plan recommendation based on the current level\",\n  \"nextSteps\": [\"List of 4-5 actionable next steps for improvement\"]\n}\n\nMake the feedback encouraging but honest, specific to the scores provided, and actionable. Consider the IELTS band descriptors when providing recommendations.`;\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        try {\n            const feedback = JSON.parse(content.text);\n            return feedback;\n        } catch (err) {\n            console.error('Failed to parse Claude response:', content.text, err);\n            throw new Error('Invalid response format from AI service');\n        }\n    } catch (error) {\n        console.error('Claude API error:', error);\n        // Fallback to mock feedback if API fails\n        return generateMockFeedback(request);\n    }\n}\nfunction generateMockFeedback(request) {\n    const { overallScore } = request;\n    // Mock feedback based on score ranges\n    if (overallScore >= 7.0) {\n        return {\n            overallAssessment: \"Excellent performance! You have demonstrated strong English proficiency across all skills. Your overall band score of \" + overallScore + \" indicates you are a competent user of English with good operational command of the language.\",\n            strengths: [\n                \"Strong overall command of English language\",\n                \"Good vocabulary range and accuracy in most contexts\",\n                \"Effective communication skills with minor inaccuracies\",\n                \"Ability to handle complex language situations\"\n            ],\n            areasForImprovement: [\n                \"Fine-tune advanced grammar structures for academic contexts\",\n                \"Expand specialized academic and professional vocabulary\",\n                \"Practice complex sentence formations and cohesive devices\",\n                \"Work on consistency across all four skills\"\n            ],\n            specificRecommendations: {\n                listening: \"Focus on academic lectures, complex discussions, and various English accents. Practice note-taking while listening to improve comprehension of detailed information.\",\n                reading: \"Practice with academic texts, research papers, and complex argumentative essays. Work on speed reading techniques while maintaining comprehension.\",\n                writing: \"Work on advanced essay structures, sophisticated argumentation, and academic writing conventions. Focus on task achievement and coherence.\",\n                speaking: \"Practice formal presentations, debates, and discussions on abstract topics. Work on fluency and natural expression of complex ideas.\"\n            },\n            studyPlan: \"Continue with advanced materials focusing on academic English. Dedicate 2-3 hours daily to practice, with emphasis on maintaining consistency across all skills. Use authentic materials like academic journals, TED talks, and formal debates.\",\n            nextSteps: [\n                \"Take regular practice tests to maintain performance level\",\n                \"Focus on any weaker skills to achieve balance across all areas\",\n                \"Consider advanced English courses or academic preparation programs\",\n                \"Practice with time constraints to improve efficiency\",\n                \"Engage with native speakers in academic or professional contexts\"\n            ]\n        };\n    } else if (overallScore >= 5.5) {\n        return {\n            overallAssessment: \"Good foundation with room for improvement in specific areas. Your overall band score of \" + overallScore + \" shows you are a modest user of English with partial command of the language, coping with overall meaning in most situations.\",\n            strengths: [\n                \"Basic communication skills are well-established\",\n                \"Understanding of fundamental grammar structures\",\n                \"Adequate vocabulary for everyday and familiar situations\",\n                \"Ability to express basic ideas and opinions clearly\"\n            ],\n            areasForImprovement: [\n                \"Expand vocabulary range for academic and professional contexts\",\n                \"Improve complex grammar usage and sentence structures\",\n                \"Enhance fluency and coherence in extended discourse\",\n                \"Develop better accuracy in language use\"\n            ],\n            specificRecommendations: {\n                listening: \"Practice with various accents, speeds, and contexts. Focus on understanding main ideas and specific details in academic and social situations.\",\n                reading: \"Work on skimming and scanning techniques. Practice with longer texts and improve vocabulary through extensive reading.\",\n                writing: \"Focus on paragraph structure, linking words, and task response. Practice both formal and informal writing styles with attention to coherence.\",\n                speaking: \"Practice pronunciation, intonation, and natural speech patterns. Work on expressing ideas clearly and developing responses fully.\"\n            },\n            studyPlan: \"Structured study plan focusing on intermediate to upper-intermediate materials. Dedicate 1-2 hours daily with balanced practice across all four skills. Use IELTS preparation materials and general English improvement resources.\",\n            nextSteps: [\n                \"Daily practice with all four skills using varied materials\",\n                \"Join English conversation groups or language exchange programs\",\n                \"Use IELTS preparation books and online resources systematically\",\n                \"Focus on building vocabulary through reading and listening\",\n                \"Take regular practice tests to track improvement\"\n            ]\n        };\n    } else {\n        return {\n            overallAssessment: \"Foundation level with significant room for improvement across all skills. Your overall band score of \" + overallScore + \" indicates limited user level, with basic competence limited to familiar situations and frequent communication breakdowns.\",\n            strengths: [\n                \"Basic understanding of English structure and patterns\",\n                \"Willingness to communicate despite limitations\",\n                \"Some vocabulary knowledge for familiar topics\",\n                \"Ability to convey basic information in simple situations\"\n            ],\n            areasForImprovement: [\n                \"Build fundamental vocabulary for everyday situations\",\n                \"Strengthen basic grammar and sentence construction\",\n                \"Improve listening comprehension for simple conversations\",\n                \"Develop basic writing skills and paragraph organization\"\n            ],\n            specificRecommendations: {\n                listening: \"Start with simple conversations, basic instructions, and familiar topics. Use visual aids and context clues to support understanding.\",\n                reading: \"Begin with short, simple texts on familiar topics. Focus on building sight vocabulary and basic comprehension skills.\",\n                writing: \"Focus on basic sentence structure, simple paragraphs, and essential grammar patterns. Practice writing about familiar topics.\",\n                speaking: \"Practice basic conversations, pronunciation of common words, and expressing simple ideas clearly and confidently.\"\n            },\n            studyPlan: \"Intensive foundation course focusing on basic English skills. Dedicate 1-2 hours daily to structured learning with emphasis on building confidence and fundamental skills. Use beginner-level materials and seek guidance from qualified teachers.\",\n            nextSteps: [\n                \"Enroll in a basic English course with qualified instruction\",\n                \"Practice daily with simple, structured materials\",\n                \"Focus on building confidence through successful communication\",\n                \"Use visual and audio aids to support learning\",\n                \"Set small, achievable goals to maintain motivation\"\n            ]\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/certificate-generator.ts":
/*!******************************************!*\
  !*** ./src/lib/certificate-generator.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCertificate: () => (/* binding */ generateCertificate)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(rsc)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nasync function generateCertificate(testResult, candidate) {\n    const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n    });\n    // Set up the certificate design\n    const pageWidth = pdf.internal.pageSize.getWidth();\n    const pageHeight = pdf.internal.pageSize.getHeight();\n    // ===== FRONT PAGE =====\n    createFrontPage(pdf, testResult, candidate, pageWidth, pageHeight);\n    // ===== BACK PAGE =====\n    pdf.addPage();\n    createBackPage(pdf, testResult, candidate, pageWidth, pageHeight);\n    // Convert to base64 string\n    const pdfBase64 = pdf.output('datauristring');\n    return pdfBase64;\n}\nfunction createFrontPage(pdf, testResult, candidate, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section with IELTS branding\n    pdf.setFillColor(0, 51, 102); // Dark blue background\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    // Main title - UPDATED (no logo)\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(20);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 30, {\n        align: 'center'\n    });\n    // Remove the old subtitle and validity text - they are no longer included\n    // Candidate details section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('CANDIDATE DETAILS', 15, 65);\n    // Create a box for candidate details\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 60);\n    // Candidate photo (right side) - ENHANCED LARGER PHOTO\n    const photoX = pageWidth - 55;\n    const photoY = 75;\n    const photoWidth = 35;\n    const photoHeight = 40;\n    // Photo border\n    pdf.setLineWidth(1);\n    pdf.rect(photoX, photoY, photoWidth, photoHeight);\n    // Add candidate photo if available\n    if (candidate.photoData) {\n        try {\n            // Prepare photo data\n            let photoData = candidate.photoData;\n            if (!photoData.startsWith('data:')) {\n                photoData = `data:image/jpeg;base64,${photoData}`;\n            }\n            // Add the photo with proper sizing\n            pdf.addImage(photoData, 'JPEG', photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2);\n        } catch (error) {\n            console.warn('Could not add photo to certificate:', error);\n            // Fallback placeholder text\n            pdf.setFontSize(8);\n            pdf.setTextColor(128, 128, 128);\n            pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n                align: 'center'\n            });\n            pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n                align: 'center'\n            });\n        }\n    } else {\n        // Placeholder text if no photo\n        pdf.setFontSize(8);\n        pdf.setTextColor(128, 128, 128);\n        pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n            align: 'center'\n        });\n        pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n            align: 'center'\n        });\n    }\n    // Candidate information (left side)\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    const leftCol = 18;\n    const rightCol = 100;\n    pdf.text('Family Name:', leftCol, 85);\n    pdf.text(candidate.fullName.split(' ').slice(-1)[0].toUpperCase(), rightCol, 85);\n    pdf.text('Given Name(s):', leftCol, 92);\n    pdf.text(candidate.fullName.split(' ').slice(0, -1).join(' ').toUpperCase(), rightCol, 92);\n    pdf.text('Candidate Number:', leftCol, 99);\n    pdf.text(candidate.candidateNumber || 'N/A', rightCol, 99);\n    pdf.text('Date of Birth:', leftCol, 106);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(candidate.dateOfBirth)), rightCol, 106);\n    pdf.text('Identification Type:', leftCol, 113);\n    pdf.text('Passport', rightCol, 113);\n    pdf.text('Identification Number:', leftCol, 120);\n    pdf.text(candidate.passportNumber, rightCol, 120);\n    pdf.text('Country/Region of Origin:', leftCol, 127);\n    pdf.text(candidate.nationality, rightCol, 127);\n    // Test details section\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST DETAILS', 15, 145);\n    // Test details box\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 150, pageWidth - 30, 25);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Test Date:', 18, 160);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(candidate.testDate)), 100, 160);\n    pdf.text('Test Centre Number:', 18, 167);\n    pdf.text('SAM001', 100, 167); // Samarkand test center code\n    pdf.text('Test Centre Name:', 18, 174);\n    pdf.text(candidate.testCenter, 100, 174);\n    // Test Results section\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST RESULTS', 15, 190);\n    // Overall Band Score section (moved to top)\n    const overallY = 195;\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(15, overallY, pageWidth - 30, 25, 'F');\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(16);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('OVERALL BAND SCORE', 20, overallY + 10);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.overallBandScore) || 0), 90, overallY + 10);\n    pdf.text(getCEFRLevel(Number(testResult.overallBandScore) || 0), 140, overallY + 10);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.overallBandScore) || 0), 170, overallY + 10);\n    pdf.setFontSize(10);\n    pdf.text('This overall band score is the average of the four skills scores, rounded to the nearest half band.', 20, overallY + 20);\n    // Results table (moved below overall score)\n    const tableStartY = 230;\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, tableStartY, pageWidth - 30, 65);\n    // Table headers\n    pdf.setFillColor(240, 240, 240);\n    pdf.rect(15, tableStartY, pageWidth - 30, 15, 'F');\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('Skill', 20, tableStartY + 10);\n    pdf.text('Band Score', 80, tableStartY + 10);\n    pdf.text('CEFR Level', 130, tableStartY + 10);\n    pdf.text('Description', 170, tableStartY + 10);\n    // Horizontal lines for table structure\n    pdf.line(15, tableStartY + 15, pageWidth - 15, tableStartY + 15);\n    pdf.line(75, tableStartY, 75, tableStartY + 65); // Skill column\n    pdf.line(125, tableStartY, 125, tableStartY + 65); // Band Score column\n    pdf.line(165, tableStartY, 165, tableStartY + 65); // CEFR column\n    // Test results data\n    const skills = [\n        {\n            name: 'Listening',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.listeningBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.listeningBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.listeningBandScore) || 0)\n        },\n        {\n            name: 'Reading',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.readingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.readingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.readingBandScore) || 0)\n        },\n        {\n            name: 'Writing',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.writingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.writingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.writingBandScore) || 0)\n        },\n        {\n            name: 'Speaking',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.speakingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.speakingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.speakingBandScore) || 0)\n        }\n    ];\n    pdf.setFont('helvetica', 'normal');\n    skills.forEach((skill, index)=>{\n        const rowY = tableStartY + 25 + index * 12;\n        // Horizontal line between rows\n        if (index > 0) {\n            pdf.line(15, rowY - 6, pageWidth - 15, rowY - 6);\n        }\n        pdf.text(skill.name, 20, rowY);\n        pdf.text(skill.band, 90, rowY);\n        pdf.text(skill.cefr, 140, rowY);\n        // Wrap description text if too long\n        const descWords = skill.description.split(' ');\n        if (descWords.length > 2) {\n            pdf.text(descWords.slice(0, 2).join(' '), 170, rowY - 2);\n            pdf.text(descWords.slice(2).join(' '), 170, rowY + 4);\n        } else {\n            pdf.text(skill.description, 170, rowY);\n        }\n    });\n}\nfunction createBackPage(pdf, testResult, candidate, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(18);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 25, {\n        align: 'center'\n    });\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Additional Information', pageWidth / 2, 35, {\n        align: 'center'\n    });\n    // Authentication section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('AUTHENTICATION', 15, 65);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 40);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Centre Stamp:', 20, 85);\n    pdf.text('Authorised Signature:', 20, 100);\n    // Certificate serial and verification info\n    pdf.text(`Certificate Serial: ${testResult.certificateSerial || 'N/A'}`, 120, 85);\n    pdf.text(`Issue Date: ${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date())}`, 120, 100);\n    // Band Score Descriptions\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('BAND SCORE DESCRIPTIONS', 15, 130);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 135, pageWidth - 30, 120);\n    pdf.setFontSize(9);\n    pdf.setFont('helvetica', 'normal');\n    const descriptions = [\n        'Band 9: Expert User - Has fully operational command of the language',\n        'Band 8: Very Good User - Has fully operational command with occasional inaccuracies',\n        'Band 7: Good User - Has operational command with occasional inaccuracies',\n        'Band 6: Competent User - Has generally effective command despite inaccuracies',\n        'Band 5: Modest User - Has partial command with frequent problems',\n        'Band 4: Limited User - Basic competence limited to familiar situations',\n        'Band 3: Extremely Limited User - Conveys general meaning in familiar situations',\n        'Band 2: Intermittent User - No real communication except basic information',\n        'Band 1: Non User - No ability to use the language'\n    ];\n    descriptions.forEach((desc, index)=>{\n        pdf.text(desc, 20, 145 + index * 12);\n    });\n    // Important Notes\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IMPORTANT NOTES', 15, 270);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 275, pageWidth - 30, 40);\n    pdf.setFontSize(9);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('• This is a MOCK certificate for practice purposes only', 20, 285);\n    pdf.text('• Not valid for official immigration or academic applications', 20, 295);\n    pdf.text('• For official IELTS certificates, visit www.ielts.org', 20, 305);\n}\n// Helper function to get CEFR level from band score\nfunction getCEFRLevel(bandScore) {\n    if (bandScore >= 8.5) return 'C2';\n    if (bandScore >= 7.0) return 'C1';\n    if (bandScore >= 5.5) return 'B2';\n    if (bandScore >= 4.0) return 'B1';\n    if (bandScore >= 3.0) return 'A2';\n    return 'A1';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/certificate-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallBandScore: () => (/* binding */ calculateOverallBandScore),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBandScore: () => (/* binding */ formatBandScore),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getBandScoreDescription: () => (/* binding */ getBandScoreDescription),\n/* harmony export */   getListeningBandScore: () => (/* binding */ getListeningBandScore),\n/* harmony export */   getReadingBandScore: () => (/* binding */ getReadingBandScore)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// IELTS band score calculation utilities\nfunction calculateOverallBandScore(listening, reading, writing, speaking) {\n    const average = (listening + reading + writing + speaking) / 4;\n    // Round to nearest 0.5\n    return Math.round(average * 2) / 2;\n}\nfunction getListeningBandScore(rawScore) {\n    // IELTS Listening band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction getReadingBandScore(rawScore) {\n    // IELTS Reading band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatBandScore(score) {\n    return score.toFixed(1);\n}\nfunction getBandScoreDescription(score) {\n    if (score >= 9.0) return 'Expert User';\n    if (score >= 8.0) return 'Very Good User';\n    if (score >= 7.0) return 'Good User';\n    if (score >= 6.0) return 'Competent User';\n    if (score >= 5.0) return 'Modest User';\n    if (score >= 4.0) return 'Limited User';\n    if (score >= 3.0) return 'Extremely Limited User';\n    if (score >= 2.0) return 'Intermittent User';\n    if (score >= 1.0) return 'Non User';\n    return 'Did not attempt the test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/certificate.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/certificate.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractYearFromSerial: () => (/* binding */ extractYearFromSerial),\n/* harmony export */   formatCertificateSerial: () => (/* binding */ formatCertificateSerial),\n/* harmony export */   generateCertificateSerial: () => (/* binding */ generateCertificateSerial),\n/* harmony export */   validateCertificateSerial: () => (/* binding */ validateCertificateSerial)\n/* harmony export */ });\n/**\n * Certificate utility functions for IELTS certification system\n */ /**\n * Generate a unique certificate serial number\n * Format: IELTS-YYYY-NNNNNN (e.g., IELTS-2024-123456)\n */ function generateCertificateSerial() {\n    const year = new Date().getFullYear();\n    const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number\n    return `IELTS-${year}-${randomNumber}`;\n}\n/**\n * Validate certificate serial number format\n */ function validateCertificateSerial(serial) {\n    const pattern = /^IELTS-\\d{4}-\\d{6}$/;\n    return pattern.test(serial);\n}\n/**\n * Extract year from certificate serial\n */ function extractYearFromSerial(serial) {\n    const match = serial.match(/^IELTS-(\\d{4})-\\d{6}$/);\n    return match ? parseInt(match[1], 10) : null;\n}\n/**\n * Format certificate serial for display\n */ function formatCertificateSerial(serial) {\n    if (!validateCertificateSerial(serial)) {\n        return serial;\n    }\n    // Add spaces for better readability: IELTS-2024-123456 -> IELTS 2024 123456\n    return serial.replace(/-/g, ' ');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/certificate.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/@noble","vendor-chunks/@paralleldrive","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/@babel","vendor-chunks/@anthropic-ai","vendor-chunks/tailwind-merge","vendor-chunks/fflate","vendor-chunks/clsx","vendor-chunks/jspdf"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();