"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/search/page",{

/***/ "(app-pages-browser)/./src/app/admin/search/page.tsx":
/*!***************************************!*\
  !*** ./src/app/admin/search/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSearchPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,Download,Edit,Eye,FileText,Filter,MapPin,RefreshCw,Search,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminSearchPage() {\n    _s();\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        query: '',\n        searchType: 'all',\n        testCenter: '',\n        testDateFrom: '',\n        testDateTo: '',\n        nationality: '',\n        hasResults: 'all',\n        resultStatus: 'all',\n        bandScoreMin: '',\n        bandScoreMax: ''\n    });\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalResults, setTotalResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const testCenters = [\n        'Innovative Centre - Samarkand'\n    ];\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleSearch = async (e)=>{\n        e === null || e === void 0 ? void 0 : e.preventDefault();\n        setIsLoading(true);\n        setHasSearched(true);\n        try {\n            const response = await fetch('/api/admin/search', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(filters)\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setResults(data.results);\n                setTotalResults(data.total);\n            } else {\n                setResults([]);\n                setTotalResults(0);\n            }\n        } catch (error) {\n            console.error('Search error:', error);\n            setResults([]);\n            setTotalResults(0);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            query: '',\n            searchType: 'all',\n            testCenter: '',\n            testDateFrom: '',\n            testDateTo: '',\n            nationality: '',\n            hasResults: 'all',\n            resultStatus: 'all',\n            bandScoreMin: '',\n            bandScoreMax: ''\n        });\n        setResults([]);\n        setHasSearched(false);\n    };\n    const exportResults = async ()=>{\n        try {\n            const response = await fetch('/api/admin/export', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    filters,\n                    format: 'csv'\n                })\n            });\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = \"ielts_search_results_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n                document.body.appendChild(a);\n                a.click();\n                window.URL.revokeObjectURL(url);\n                document.body.removeChild(a);\n            }\n        } catch (error) {\n            console.error('Export error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Advanced Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Search and filter candidates and test results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: exportResults,\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            \"Export Results\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"searchType\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Search Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"searchType\",\n                                            value: filters.searchType,\n                                            onChange: (e)=>handleFilterChange('searchType', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Fields\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"Full Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"passport\",\n                                                    children: \"Passport Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"query\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Search Query\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"query\",\n                                                    value: filters.query,\n                                                    onChange: (e)=>handleFilterChange('query', e.target.value),\n                                                    placeholder: \"Enter search term...\",\n                                                    className: \"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowAdvanced(!showAdvanced),\n                                    className: \"inline-flex items-center text-sm text-blue-600 hover:text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        showAdvanced ? 'Hide' : 'Show',\n                                        \" Advanced Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: clearFilters,\n                                            className: \"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 inline\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\",\n                                            children: isLoading ? 'Searching...' : 'Search'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"testCenter\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Test Center\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"testCenter\",\n                                            value: filters.testCenter,\n                                            onChange: (e)=>handleFilterChange('testCenter', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Centers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                testCenters.map((center)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: center,\n                                                        children: center\n                                                    }, center, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"testDateFrom\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Test Date From\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            id: \"testDateFrom\",\n                                            value: filters.testDateFrom,\n                                            onChange: (e)=>handleFilterChange('testDateFrom', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"testDateTo\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Test Date To\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            id: \"testDateTo\",\n                                            value: filters.testDateTo,\n                                            onChange: (e)=>handleFilterChange('testDateTo', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"nationality\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Nationality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"nationality\",\n                                            value: filters.nationality,\n                                            onChange: (e)=>handleFilterChange('nationality', e.target.value),\n                                            placeholder: \"e.g., British, American\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"hasResults\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Has Test Results\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"hasResults\",\n                                            value: filters.hasResults,\n                                            onChange: (e)=>handleFilterChange('hasResults', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Candidates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"yes\",\n                                                    children: \"With Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"no\",\n                                                    children: \"Without Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"resultStatus\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Result Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"resultStatus\",\n                                            value: filters.resultStatus,\n                                            onChange: (e)=>handleFilterChange('resultStatus', e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pending\",\n                                                    children: \"Pending\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"verified\",\n                                                    children: \"Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"bandScoreMin\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Min Band Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            id: \"bandScoreMin\",\n                                            value: filters.bandScoreMin,\n                                            onChange: (e)=>handleFilterChange('bandScoreMin', e.target.value),\n                                            min: \"1\",\n                                            max: \"9\",\n                                            step: \"0.5\",\n                                            placeholder: \"1.0\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"bandScoreMax\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Max Band Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            id: \"bandScoreMax\",\n                                            value: filters.bandScoreMax,\n                                            onChange: (e)=>handleFilterChange('bandScoreMax', e.target.value),\n                                            min: \"1\",\n                                            max: \"9\",\n                                            step: \"0.5\",\n                                            placeholder: \"9.0\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: [\n                                        \"Search Results \",\n                                        totalResults > 0 && \"(\".concat(totalResults, \")\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this),\n                                        results.filter((r)=>r.hasResults).length,\n                                        \" with results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Searching...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 13\n                    }, this) : results.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 hover:bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                result.photoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-12 w-12 rounded-full object-cover\",\n                                                    src: result.photoUrl,\n                                                    alt: result.fullName,\n                                                    width: 48,\n                                                    height: 48\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: result.fullName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 flex items-center space-x-4 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        result.passportNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        new Date(result.testDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        result.testCenter\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                result.email,\n                                                                \" • \",\n                                                                result.nationality\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                result.hasResults && result.result ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Band: \",\n                                                                result.result.overallBandScore || 'N/A'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(result.result.status === 'completed' ? 'bg-green-100 text-green-800' : result.result.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'),\n                                                            children: result.result.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"No results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/admin/candidates/\".concat(result.id),\n                                                            className: \"text-blue-600 hover:text-blue-900\",\n                                                            title: \"View Details\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/admin/candidates/\".concat(result.id, \"/edit\"),\n                                                            className: \"text-green-600 hover:text-green-900\",\n                                                            title: \"Edit Candidate\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 19\n                                }, this)\n                            }, result.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_Download_Edit_Eye_FileText_Filter_MapPin_RefreshCw_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No results found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search criteria or filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\admin\\\\search\\\\page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminSearchPage, \"I3oy4IOgKLMng/BVPUQlhHUIWPU=\");\n_c = AdminSearchPage;\nvar _c;\n$RefreshReg$(_c, \"AdminSearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/search/page.tsx\n"));

/***/ })

});